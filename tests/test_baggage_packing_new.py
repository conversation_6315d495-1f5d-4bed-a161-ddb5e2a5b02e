#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
行李打包新修改需求测试
"""

import sys
import os
sys.path.append('/home/<USER>/workspace/flight/vj_crawler')

from app.services.agent_helper import extract_baggage_price_from_select_value


def test_extract_baggage_price():
    """测试从select_value中提取行李价格"""
    print("=== 测试extract_baggage_price_from_select_value函数 ===")
    
    # 测试用例1：正常情况
    test_value1 = "1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1"
    price1 = extract_baggage_price_from_select_value(test_value1)
    print(f"测试用例1: {test_value1}")
    print(f"提取的价格: {price1}")
    assert price1 == 55.3, f"期望55.3，实际{price1}"
    
    # 测试用例2：不同价格
    test_value2 = "1|NA|3|68189641|120.5|False|3|Bag 25kgs||NA||0|Baggage 25kgs|120.50 CNY|120.5|4.42|0|0|1"
    price2 = extract_baggage_price_from_select_value(test_value2)
    print(f"测试用例2: {test_value2}")
    print(f"提取的价格: {price2}")
    assert price2 == 120.5, f"期望120.5，实际{price2}"
    
    # 测试用例3：异常情况
    test_value3 = "invalid|format"
    price3 = extract_baggage_price_from_select_value(test_value3)
    print(f"测试用例3: {test_value3}")
    print(f"提取的价格: {price3}")
    assert price3 == 0.0, f"期望0.0，实际{price3}"
    
    print("✅ extract_baggage_price_from_select_value函数测试通过")


def test_all_fares_structure():
    """测试all_fares新的数据结构"""
    print("\n=== 测试all_fares新的数据结构 ===")
    
    # 模拟新的all_fares结构
    all_fares = {
        "0kg": {
            "adult": {"base": 100.0, "tax": 20.0},
            "child": {"base": 80.0, "tax": 16.0},
            "infant": {"base": 0.0, "tax": 0.0},
            "fare_key": "10,ECO_FARE_KEY",
            "cabin": "ECO"
        },
        "20kg": {
            "adult": {"base": 150.0, "tax": 30.0},
            "child": {"base": 120.0, "tax": 24.0},
            "infant": {"base": 0.0, "tax": 0.0},
            "fare_key": "8,DLX_FARE_KEY",
            "cabin": "DLX"
        }
    }
    
    print("新的all_fares结构:")
    for key, value in all_fares.items():
        print(f"  {key}: {value}")
    
    # 验证结构
    assert "0kg" in all_fares, "应该包含0kg舱位"
    assert "20kg" in all_fares, "应该包含20kg舱位"
    assert all_fares["0kg"]["cabin"] == "ECO", "0kg应该是ECO舱位"
    assert all_fares["20kg"]["cabin"] == "DLX", "20kg应该是DLX舱位"
    
    print("✅ all_fares数据结构测试通过")


def test_price_comparison_logic():
    """测试价格对比逻辑"""
    print("\n=== 测试价格对比逻辑 ===")
    
    # 模拟数据
    all_fares = {
        "0kg": {
            "adult": {"base": 100.0, "tax": 20.0},
            "fare_key": "10,ECO_FARE_KEY",
        },
        "20kg": {
            "adult": {"base": 150.0, "tax": 30.0},
            "fare_key": "8,DLX_FARE_KEY",
        }
    }
    
    # 计算最低舱位价格
    lowest_fare_total = all_fares["0kg"]["adult"]["base"] + all_fares["0kg"]["adult"]["tax"]
    print(f"最低舱位价格: {lowest_fare_total}")
    
    # 模拟行李价格
    baggage_total = 55.3  # 从select_value提取的价格
    print(f"行李价格: {baggage_total}")
    
    # 计算20kg舱位价格
    dlx_fare_total = all_fares["20kg"]["adult"]["base"] + all_fares["20kg"]["adult"]["tax"]
    print(f"20kg舱位价格: {dlx_fare_total}")
    
    # 价格对比
    eco_with_baggage = lowest_fare_total + baggage_total
    print(f"最低舱位+行李: {eco_with_baggage}")
    print(f"20kg舱位: {dlx_fare_total}")
    
    should_switch = dlx_fare_total < eco_with_baggage
    print(f"是否应该切换到20kg舱位: {should_switch}")
    
    # 在这个例子中，180 < 175.3 是False，所以不应该切换
    assert not should_switch, "在这个例子中不应该切换"
    
    # 测试应该切换的情况
    all_fares["20kg"]["adult"]["base"] = 120.0  # 降低20kg舱位价格
    dlx_fare_total = all_fares["20kg"]["adult"]["base"] + all_fares["20kg"]["adult"]["tax"]
    should_switch = dlx_fare_total < eco_with_baggage
    print(f"降低20kg舱位价格后: {dlx_fare_total}")
    print(f"是否应该切换: {should_switch}")
    assert should_switch, "降低价格后应该切换"
    
    print("✅ 价格对比逻辑测试通过")


def test_passengers_with_baggage():
    """测试乘客行李数据结构"""
    print("\n=== 测试乘客行李数据结构 ===")
    
    # 模拟乘客数据
    passengers = [
        {
            "passenger_type": "adult",
            "first_name": "John",
            "last_name": "Doe",
            "baggages": [
                {
                    "aux_type": "baggage",
                    "weight": 20,
                    "from_select_value": "1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1"
                }
            ]
        }
    ]
    
    print("乘客数据结构:")
    for i, passenger in enumerate(passengers):
        print(f"  乘客{i+1}: {passenger['first_name']} {passenger['last_name']}")
        for j, baggage in enumerate(passenger.get('baggages', [])):
            print(f"    行李{j+1}: {baggage['weight']}kg")
            if baggage.get('from_select_value'):
                price = extract_baggage_price_from_select_value(baggage['from_select_value'])
                print(f"    价格: {price}")
    
    print("✅ 乘客行李数据结构测试通过")


if __name__ == "__main__":
    print("🚀 开始测试行李打包新修改需求")
    
    try:
        test_extract_baggage_price()
        test_all_fares_structure()
        test_price_comparison_logic()
        test_passengers_with_baggage()
        
        print("\n🎉 所有测试通过！行李打包新修改需求实现正确")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
