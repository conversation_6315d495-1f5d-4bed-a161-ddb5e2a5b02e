import requests

cookies = {
    '_gcl_au': '1.1.844287800.1734088068',
    '_tt_enable_cookie': '1',
    '_ttp': '0uSTn5R2rmHvIz4zrlsXtMIUcGl.tt.1',
    '_fbp': 'fb.1.1734088069988.517884065333492022',
    '_ga': 'GA1.1.284997161.1734088071',
    '_atrk_siteuid': '5ogrHr6W4G10s9lZ',
    '_hjSessionUser_3486574': 'eyJpZCI6IjQ4MGY5NDVkLTdjNWUtNWMyNi1hZjIzLTgxYTA3NzFjNGEyYSIsImNyZWF0ZWQiOjE3MzQwODgwNzIwMTIsImV4aXN0aW5nIjp0cnVlfQ==',
    'booking_cookie_agreement_enable': 'eyJpdiI6Imt5M1lYY21PSWpZbXhBOVV4K2doM1E9PSIsInZhbHVlIjoiZ1NoTzhhb3QyeFVKSDRJaU5DY0RnSGZWenJmdWU1MHFtMUE5TzB5bnc5ckZiQWZFeWdwUFFEN0dMNFlLdVdVankxRW54a1Z4ZXYyQXFMaCtFZzk1ejkySUpwNjdTWExQNGZPbDdrZHNWb0s4Q2dMbTExK3RLckd5eWQ0YjBuK2o0LzdZU3lnOTIwbW95MUhNY1VZcGdBPT0iLCJtYWMiOiJiNjRhMjY0NGE2YjcyM2M3ZjU2N2I3ZTI0NjU0YTE0ZTBiZDQ0YTIzYTgxNDcwOTNlZjI4MTRmYTkyMTU4YWU1In0%3D',
    '_ga_R4BKHWQBL7': 'GS1.1.1735800766.1.0.1735800769.0.0.0',
    'mp_858bc9dffd91b52dd1a4d241b1b18bf5_mixpanel': '%7B%22distinct_id%22%3A%20%22a4ab7a3d-e6b2-4606-af4b-adcdfb157151%22%2C%22%24device_id%22%3A%20%221945f03dde85888-02d449ff5befab-1e525636-1fa400-1945f03dde85888%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%2C%22mp_lib%22%3A%20%22Rudderstack%3A%20web%22%2C%22utm_source%22%3A%20%22frontpage%22%2C%22%24initial_referrer%22%3A%20%22https%3A%2F%2Fth.vietjetair.com%2F%22%2C%22%24initial_referring_domain%22%3A%20%22th.vietjetair.com%22%2C%22%24user_id%22%3A%20%22a4ab7a3d-e6b2-4606-af4b-adcdfb157151%22%2C%22currency%22%3A%20%22USD%22%2C%22isMobile%22%3A%20false%2C%22locale%22%3A%20%22en%22%2C%22originalReferrer%22%3A%20%22%22%2C%22partner%22%3A%20%22vietjetair%22%2C%22residency%22%3A%20%22TH%22%7D',
    'datadome': 'bKERr4HkWCfedqTyX9S3aPGIOzN78hlc_UdEnt2hYguulljJMM1btQZqNU0a7pJTs4JgG8WFqaskkpe8Dpwc_fpDWR6iKNNjj~rPrHuJJzCUoutJjpHD4CWoJPC0STKV',
    '_ga_4C6KWLX3N7': 'GS1.1.1737030161.2.1.1737030295.0.0.0',
    '_ga_1DQMSR0C48': 'deleted',
    'appier_utmz': '%7B%22csr%22%3A%22th.vietjetair.com%22%2C%22timestamp%22%3A1738726897%2C%22lcsr%22%3A%22th.vietjetair.com%22%7D',
    'appier_random_unique_id_ViewLanding_VietJetAir': '7RBVt8F7InkX7FWrCAYVuQ',
    'cto_bundle': 'DfIMtF9zb0VRdHlDWnFWV1FWSEJncWwlMkY1bWFybFNObnV1d3pQVFY3YmEzSm5IZ3FiZ2J3aTBuMHlvN1NRNWRlMndjJTJGV0Z0OEo4R3hhZGJkSWZpSHpkMUt2WCUyRlVjOHZWdlhMMk83YU5DMXRzSVJ1OWJXbFRiJTJCeGtQUTc5S283RmVMYTRpRW9GTWhqVyUyQnFEUzJrVWRwRXVrbUVTUHdHUnRxclZ6YlU1M2l4b3cwZ2VFJTNE',
    '_ga_KML2W7BV5C': 'GS1.1.1738834133.43.0.1738834133.60.0.0',
    '_ga_6ZGCC82BTL': 'GS1.1.1738834139.8.0.1738834139.0.0.841254166',
    '_ga_Z47TKGJW5K': 'GS1.1.1738842893.48.0.1738842893.60.0.0',
    'flight_filter': '?tripType=onewaytrip&currency=thb&from_where=BKK&start=10%2F02%2F2025&to_where=CEI&end=10%2F02%2F2025&adultCount=2&childCount=0&infantCount=0&promoCode=&findLowestFare=',
    '_ga_1DQMSR0C48': 'GS1.1.1738843812.31.1.1738844005.0.0.0',
    'AWSALBTG': '6SpT/PwRhJJ9XWa51PCQNR7DCwZJ889e8byJv7hdm/dmENS5vnZLgMobWCLkMyYRPNqkR+YG80vOk1G8b6N13qzamNQsK6PCb5LmkpigWJI3ntDioEZnmug7iRBq6N51Qdf02uaer1NTQ/DLlGqKXe/wRP3XoytqD3B+dhJT7v3Z',
    'AWSALBTGCORS': '6SpT/PwRhJJ9XWa51PCQNR7DCwZJ889e8byJv7hdm/dmENS5vnZLgMobWCLkMyYRPNqkR+YG80vOk1G8b6N13qzamNQsK6PCb5LmkpigWJI3ntDioEZnmug7iRBq6N51Qdf02uaer1NTQ/DLlGqKXe/wRP3XoytqD3B+dhJT7v3Z',
    'XSRF-TOKEN': 'eyJpdiI6IlZXYzlqRlVXOHhpcXlnR21ieU5DRnc9PSIsInZhbHVlIjoiYTI4S3NTdEhGTHRKR2kzeHRDTjhQdUdmSHYySllrWjc1ZmxZYmZGTENEN2Fia1l4SEVTODZWcmI5cGRHUW5EWEZldUhBRTRQOC96WFljcFFjVEt3U2s2anRKZDRVdXkxUDNzR3BjN2NzS2V6bUVlakE3YWtYZTlmQVhDNnRuRmMiLCJtYWMiOiJlMWZhYzU2ZGE5MzE4Nzg4YmM1MDczNGM5NzJmNDQwMmY5ZGNjYjE3OTc1ZjM5ODcyZGIzYTgzNjNhMTU5YjAyIn0%3D',
    'thaivietjet_session': 'eyJpdiI6ImlMRVhzemxRdXUvL1BvM2IraTU5UXc9PSIsInZhbHVlIjoiUXRlb04vR0pRbFZVdzQ3cVRBT1NrbXd0QWVsLzM1eGRjZjFwelMvK3FzZlVUQXdac0tDOVdqbEFqT2hnbW1QYXh3UnhZWnV0ZlZPQ01jOS9BbEwzeDhWU1RNTTY1a3oxUnNhYng1Ly9MbnJJUCtOTW5MeC90bW90UXNZeUN1dFQiLCJtYWMiOiI5NjExZjliZDY2OTIwZmJhNGIxYzhhMThiYjc0OGE0YmZlM2UxYjkyMjkxNzQ3MGJmMmFkNzgyYmYxMWI0YTkwIn0%3D',
}

headers = {
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    # 'cookie': '_gcl_au=1.1.844287800.1734088068; _tt_enable_cookie=1; _ttp=0uSTn5R2rmHvIz4zrlsXtMIUcGl.tt.1; _fbp=fb.1.1734088069988.517884065333492022; _ga=GA1.1.284997161.1734088071; _atrk_siteuid=5ogrHr6W4G10s9lZ; _hjSessionUser_3486574=eyJpZCI6IjQ4MGY5NDVkLTdjNWUtNWMyNi1hZjIzLTgxYTA3NzFjNGEyYSIsImNyZWF0ZWQiOjE3MzQwODgwNzIwMTIsImV4aXN0aW5nIjp0cnVlfQ==; booking_cookie_agreement_enable=eyJpdiI6Imt5M1lYY21PSWpZbXhBOVV4K2doM1E9PSIsInZhbHVlIjoiZ1NoTzhhb3QyeFVKSDRJaU5DY0RnSGZWenJmdWU1MHFtMUE5TzB5bnc5ckZiQWZFeWdwUFFEN0dMNFlLdVdVankxRW54a1Z4ZXYyQXFMaCtFZzk1ejkySUpwNjdTWExQNGZPbDdrZHNWb0s4Q2dMbTExK3RLckd5eWQ0YjBuK2o0LzdZU3lnOTIwbW95MUhNY1VZcGdBPT0iLCJtYWMiOiJiNjRhMjY0NGE2YjcyM2M3ZjU2N2I3ZTI0NjU0YTE0ZTBiZDQ0YTIzYTgxNDcwOTNlZjI4MTRmYTkyMTU4YWU1In0%3D; _ga_R4BKHWQBL7=GS1.1.1735800766.1.0.1735800769.0.0.0; mp_858bc9dffd91b52dd1a4d241b1b18bf5_mixpanel=%7B%22distinct_id%22%3A%20%22a4ab7a3d-e6b2-4606-af4b-adcdfb157151%22%2C%22%24device_id%22%3A%20%221945f03dde85888-02d449ff5befab-1e525636-1fa400-1945f03dde85888%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%2C%22mp_lib%22%3A%20%22Rudderstack%3A%20web%22%2C%22utm_source%22%3A%20%22frontpage%22%2C%22%24initial_referrer%22%3A%20%22https%3A%2F%2Fth.vietjetair.com%2F%22%2C%22%24initial_referring_domain%22%3A%20%22th.vietjetair.com%22%2C%22%24user_id%22%3A%20%22a4ab7a3d-e6b2-4606-af4b-adcdfb157151%22%2C%22currency%22%3A%20%22USD%22%2C%22isMobile%22%3A%20false%2C%22locale%22%3A%20%22en%22%2C%22originalReferrer%22%3A%20%22%22%2C%22partner%22%3A%20%22vietjetair%22%2C%22residency%22%3A%20%22TH%22%7D; datadome=bKERr4HkWCfedqTyX9S3aPGIOzN78hlc_UdEnt2hYguulljJMM1btQZqNU0a7pJTs4JgG8WFqaskkpe8Dpwc_fpDWR6iKNNjj~rPrHuJJzCUoutJjpHD4CWoJPC0STKV; _ga_4C6KWLX3N7=GS1.1.1737030161.2.1.1737030295.0.0.0; _ga_1DQMSR0C48=deleted; appier_utmz=%7B%22csr%22%3A%22th.vietjetair.com%22%2C%22timestamp%22%3A1738726897%2C%22lcsr%22%3A%22th.vietjetair.com%22%7D; appier_random_unique_id_ViewLanding_VietJetAir=7RBVt8F7InkX7FWrCAYVuQ; cto_bundle=DfIMtF9zb0VRdHlDWnFWV1FWSEJncWwlMkY1bWFybFNObnV1d3pQVFY3YmEzSm5IZ3FiZ2J3aTBuMHlvN1NRNWRlMndjJTJGV0Z0OEo4R3hhZGJkSWZpSHpkMUt2WCUyRlVjOHZWdlhMMk83YU5DMXRzSVJ1OWJXbFRiJTJCeGtQUTc5S283RmVMYTRpRW9GTWhqVyUyQnFEUzJrVWRwRXVrbUVTUHdHUnRxclZ6YlU1M2l4b3cwZ2VFJTNE; _ga_KML2W7BV5C=GS1.1.1738834133.43.0.1738834133.60.0.0; _ga_6ZGCC82BTL=GS1.1.1738834139.8.0.1738834139.0.0.841254166; _ga_Z47TKGJW5K=GS1.1.1738842893.48.0.1738842893.60.0.0; flight_filter=?tripType=onewaytrip&currency=thb&from_where=BKK&start=10%2F02%2F2025&to_where=CEI&end=10%2F02%2F2025&adultCount=2&childCount=0&infantCount=0&promoCode=&findLowestFare=; _ga_1DQMSR0C48=GS1.1.1738843812.31.1.1738844005.0.0.0; AWSALBTG=6SpT/PwRhJJ9XWa51PCQNR7DCwZJ889e8byJv7hdm/dmENS5vnZLgMobWCLkMyYRPNqkR+YG80vOk1G8b6N13qzamNQsK6PCb5LmkpigWJI3ntDioEZnmug7iRBq6N51Qdf02uaer1NTQ/DLlGqKXe/wRP3XoytqD3B+dhJT7v3Z; AWSALBTGCORS=6SpT/PwRhJJ9XWa51PCQNR7DCwZJ889e8byJv7hdm/dmENS5vnZLgMobWCLkMyYRPNqkR+YG80vOk1G8b6N13qzamNQsK6PCb5LmkpigWJI3ntDioEZnmug7iRBq6N51Qdf02uaer1NTQ/DLlGqKXe/wRP3XoytqD3B+dhJT7v3Z; XSRF-TOKEN=eyJpdiI6IlZXYzlqRlVXOHhpcXlnR21ieU5DRnc9PSIsInZhbHVlIjoiYTI4S3NTdEhGTHRKR2kzeHRDTjhQdUdmSHYySllrWjc1ZmxZYmZGTENEN2Fia1l4SEVTODZWcmI5cGRHUW5EWEZldUhBRTRQOC96WFljcFFjVEt3U2s2anRKZDRVdXkxUDNzR3BjN2NzS2V6bUVlakE3YWtYZTlmQVhDNnRuRmMiLCJtYWMiOiJlMWZhYzU2ZGE5MzE4Nzg4YmM1MDczNGM5NzJmNDQwMmY5ZGNjYjE3OTc1ZjM5ODcyZGIzYTgzNjNhMTU5YjAyIn0%3D; thaivietjet_session=eyJpdiI6ImlMRVhzemxRdXUvL1BvM2IraTU5UXc9PSIsInZhbHVlIjoiUXRlb04vR0pRbFZVdzQ3cVRBT1NrbXd0QWVsLzM1eGRjZjFwelMvK3FzZlVUQXdac0tDOVdqbEFqT2hnbW1QYXh3UnhZWnV0ZlZPQ01jOS9BbEwzeDhWU1RNTTY1a3oxUnNhYng1Ly9MbnJJUCtOTW5MeC90bW90UXNZeUN1dFQiLCJtYWMiOiI5NjExZjliZDY2OTIwZmJhNGIxYzhhMThiYjc0OGE0YmZlM2UxYjkyMjkxNzQ3MGJmMmFkNzgyYmYxMWI0YTkwIn0%3D',
    'origin': 'https://th.vietjetair.com',
    'priority': 'u=1, i',
    'referer': 'https://th.vietjetair.com/booking/JXI7L5KF4/passenger',
    'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-csrf-token': 'Fl7jwLmNUJ8UP3wfVZM2ujej2s099yjOwpsmfxaI',
    'x-requested-with': 'XMLHttpRequest',
}

data = {
    'code': 'JXI7L5KF4',
    'first_name': 'MING',
    'last_name': 'ZHANG',
    'country': 'CHN',
    'area_code': '+86',
    'phone': '13818432182',
    'email': '<EMAIL>',
    'pax_adult_id[]': ['', ''],
    'pax_adult_funid[]': ['', ''],
    'pax_adult_gender[]': ['Male', 'Female'],
    'pax_adult_first_name[]': ['LIANG', 'MING'],
    'pax_adult_last_name[]': ['RONG', 'RONG'],
    'pax_adult_dob[]': ['01/01/1990', '02/02/1992'],
    'pax_adult_nationality[]': ['CHN', 'CHN'],
    'applyVoucher': 'false',
    'serial': '',
    'pin': '',
    'password': '',
    'free_services[0][]': '',
    'paxes_inf[0][ancillary][0][Baggage]': 'Baggage',
    'paxes_inf[0][ancillary][0][Cabin Baggage]': 'Cabin Baggage',
    'paxes_inf[0][ancillary][0][Meal]': 'Meal',
    'paxes_inf[0][ancillary][0][Priority]': 'Priority',
    'paxes_inf[0][ancillary][0][Vip Lounge]': 'Vip Lounge',
    'paxes_inf[0][ancillary][0][Special Service]': 'Special Service',
    'paxes_inf[0][type]': 'adult',
    'paxes_inf[0][first_name]': 'LIANG',
    'paxes_inf[0][last_name]': 'RONG',
    'paxes_inf[0][gender]': 'Male',
    'paxes_inf[0][dob]': '01/01/1990',
    'paxes_inf[0][passport]': '',
    'paxes_inf[0][nationality]': 'CHN',
    'paxes_inf[0][funid]': '',
    'paxes_inf[1][ancillary][0][Baggage]': 'Baggage',
    'paxes_inf[1][ancillary][0][Cabin Baggage]': 'Cabin Baggage',
    'paxes_inf[1][ancillary][0][Meal]': 'Meal',
    'paxes_inf[1][ancillary][0][Priority]': 'Priority',
    'paxes_inf[1][ancillary][0][Vip Lounge]': 'Vip Lounge',
    'paxes_inf[1][ancillary][0][Special Service]': 'Special Service',
    'paxes_inf[1][type]': 'adult',
    'paxes_inf[1][first_name]': 'MING',
    'paxes_inf[1][last_name]': 'RONG',
    'paxes_inf[1][gender]': 'Female',
    'paxes_inf[1][dob]': '02/02/1992',
    'paxes_inf[1][passport]': '',
    'paxes_inf[1][nationality]': 'CHN',
    'paxes_inf[1][funid]': '',
    'selected_addon_inf[0][0][title]': 'Seat',
    'selected_addon_inf[0][0][key]': 'Seat',
    'selected_addon_inf[0][0][totalAmount]': '0',
    'selected_addon_inf[0][0][baseAmount]': '0',
    'selected_addon_inf[0][0][taxAmount]': '0',
    'selected_addon_inf[0][0][quantity]': '0',
    'selected_addon_inf[0][1][title]': 'Baggages',
    'selected_addon_inf[0][1][key]': 'Baggage',
    'selected_addon_inf[0][1][totalAmount]': '0',
    'selected_addon_inf[0][1][baseAmount]': '0',
    'selected_addon_inf[0][1][taxAmount]': '0',
    'selected_addon_inf[0][1][quantity]': '0',
    'selected_addon_inf[0][2][title]': 'Cabin Baggage',
    'selected_addon_inf[0][2][key]': 'Cabin Baggage',
    'selected_addon_inf[0][2][totalAmount]': '0',
    'selected_addon_inf[0][2][baseAmount]': '0',
    'selected_addon_inf[0][2][taxAmount]': '0',
    'selected_addon_inf[0][2][quantity]': '0',
    'selected_addon_inf[0][3][title]': 'Hot meals',
    'selected_addon_inf[0][3][key]': 'Meal',
    'selected_addon_inf[0][3][totalAmount]': '0',
    'selected_addon_inf[0][3][baseAmount]': '0',
    'selected_addon_inf[0][3][taxAmount]': '0',
    'selected_addon_inf[0][3][quantity]': '0',
    'selected_addon_inf[0][4][title]': 'Priority Checkin',
    'selected_addon_inf[0][4][key]': 'Priority',
    'selected_addon_inf[0][4][totalAmount]': '0',
    'selected_addon_inf[0][4][baseAmount]': '0',
    'selected_addon_inf[0][4][taxAmount]': '0',
    'selected_addon_inf[0][4][quantity]': '0',
    'selected_addon_inf[0][5][title]': 'Vip Lounge',
    'selected_addon_inf[0][5][key]': 'Vip Lounge',
    'selected_addon_inf[0][5][totalAmount]': '0',
    'selected_addon_inf[0][5][baseAmount]': '0',
    'selected_addon_inf[0][5][taxAmount]': '0',
    'selected_addon_inf[0][5][quantity]': '0',
    'selected_addon_inf[0][6][title]': 'Special Service',
    'selected_addon_inf[0][6][key]': 'Special Service',
    'selected_addon_inf[0][6][totalAmount]': '0',
    'selected_addon_inf[0][6][baseAmount]': '0',
    'selected_addon_inf[0][6][taxAmount]': '0',
    'selected_addon_inf[0][6][quantity]': '0',
    'selected_addon_inf[0][7][title]': 'VAT',
    'selected_addon_inf[0][7][key]': 'tax',
    'selected_addon_inf[0][7][totalAmount]': '0',
    'selected_addon_inf[0][7][baseAmount]': '0',
    'selected_addon_inf[0][7][taxAmount]': '0',
    'selected_addon_inf[0][7][quantity]': '1',
    'selected_other_services[0][0][title]': 'Travel Insurance',
    'selected_other_services[0][0][key]': 'Insurance',
    'selected_other_services[0][0][totalAmount]': '0',
    'selected_other_services[0][0][baseAmount]': '0',
    'selected_other_services[0][0][taxAmount]': '0',
    'selected_other_services[0][0][quantity]': '0',
}

response = requests.post('https://th.vietjetair.com/booking/quotation', cookies=cookies, headers=headers, data=data)
print(response.text)
