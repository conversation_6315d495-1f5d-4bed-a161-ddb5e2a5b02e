#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试travel_options方法的button参数
"""

import sys
import os
sys.path.append('/home/<USER>/workspace/flight/vj_crawler')

from unittest.mock import Mock, patch
from app.clients.agent_client import VZAgentClient


def test_travel_options_button_parameter():
    """测试travel_options方法的button参数"""
    print("=== 测试travel_options方法的button参数 ===")
    
    # 创建客户端实例
    client = VZAgentClient()
    client._view_state = 'test_view_state'
    client._view_state_generator = 'test_generator'
    client._debug_id = 'test_debug_id'
    
    # Mock switch_request方法
    with patch.object(client, 'switch_request') as mock_switch_request:
        mock_switch_request.return_value = 'mocked_response'
        
        # 测试默认button参数（continue）
        print("测试1: 默认button参数")
        result = client.travel_options(fare_key='test_fare_key')
        
        # 验证调用参数
        args, kwargs = mock_switch_request.call_args
        url = args[0]
        form_data = kwargs['form_data']
        
        print(f"URL: {url}")
        print(f"Form data: {form_data}")
        
        # 检查button参数
        button_found = False
        for item in form_data:
            if item[0] == 'button':
                print(f"Button参数: {item[1]}")
                assert item[1] == 'continue', f"期望'continue'，实际'{item[1]}'"
                button_found = True
                break
        
        assert button_found, "未找到button参数"
        print("✅ 默认button参数测试通过")
        
        # 测试自定义button参数（back）
        print("\n测试2: 自定义button参数（back）")
        result = client.travel_options(fare_key='test_fare_key', button='back')
        
        # 验证调用参数
        args, kwargs = mock_switch_request.call_args
        form_data = kwargs['form_data']
        
        # 检查button参数
        button_found = False
        for item in form_data:
            if item[0] == 'button':
                print(f"Button参数: {item[1]}")
                assert item[1] == 'back', f"期望'back'，实际'{item[1]}'"
                button_found = True
                break
        
        assert button_found, "未找到button参数"
        print("✅ 自定义button参数测试通过")
        
        # 测试其他button参数
        print("\n测试3: 其他button参数（custom）")
        result = client.travel_options(fare_key='test_fare_key', button='custom')
        
        # 验证调用参数
        args, kwargs = mock_switch_request.call_args
        form_data = kwargs['form_data']
        
        # 检查button参数
        button_found = False
        for item in form_data:
            if item[0] == 'button':
                print(f"Button参数: {item[1]}")
                assert item[1] == 'custom', f"期望'custom'，实际'{item[1]}'"
                button_found = True
                break
        
        assert button_found, "未找到button参数"
        print("✅ 其他button参数测试通过")


def test_travel_options_without_fare_key():
    """测试没有fare_key时的情况"""
    print("\n=== 测试没有fare_key时的情况 ===")
    
    client = VZAgentClient()
    
    with patch.object(client, 'switch_request') as mock_switch_request:
        mock_switch_request.return_value = 'mocked_response'
        
        # 测试没有fare_key的情况
        result = client.travel_options()
        
        # 验证调用参数
        args, kwargs = mock_switch_request.call_args
        url = args[0]
        form_data = kwargs['form_data']
        
        print(f"URL: {url}")
        print(f"Form data: {form_data}")
        
        # 没有fare_key时，form_data应该是None
        assert form_data is None, f"期望None，实际{form_data}"
        print("✅ 没有fare_key时的测试通过")


def test_form_data_structure():
    """测试form_data的完整结构"""
    print("\n=== 测试form_data的完整结构 ===")
    
    client = VZAgentClient()
    client._view_state = 'test_view_state'
    client._view_state_generator = 'test_generator'
    client._debug_id = 'test_debug_id'
    
    with patch.object(client, 'switch_request') as mock_switch_request:
        mock_switch_request.return_value = 'mocked_response'
        
        result = client.travel_options(fare_key='test_fare_key', button='back')
        
        args, kwargs = mock_switch_request.call_args
        form_data = kwargs['form_data']
        
        print("Form data结构:")
        expected_fields = [
            '__VIEWSTATE',
            '__VIEWSTATEGENERATOR', 
            'button',
            'SesID',
            'DebugID',
            'gridTravelOptDep',
            'OperatedBy'
        ]
        
        form_dict = dict(form_data)
        for field in expected_fields:
            assert field in form_dict, f"缺少字段: {field}"
            print(f"  {field}: {form_dict[field]}")
        
        # 验证关键字段的值
        assert form_dict['__VIEWSTATE'] == 'test_view_state'
        assert form_dict['__VIEWSTATEGENERATOR'] == 'test_generator'
        assert form_dict['button'] == 'back'
        assert form_dict['gridTravelOptDep'] == 'test_fare_key'
        
        print("✅ form_data结构测试通过")


if __name__ == "__main__":
    print("🚀 开始测试travel_options方法的button参数")
    
    try:
        test_travel_options_button_parameter()
        test_travel_options_without_fare_key()
        test_form_data_structure()
        
        print("\n🎉 所有测试通过！travel_options方法的button参数工作正常")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
