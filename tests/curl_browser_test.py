#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
apt install libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1 libxcomposite1 libxrandr2 libxcb1
"""

from curl_cffi import requests
from loguru import logger

BROWSER_TYPES = [
    # Edge
    "edge99",
    "edge101",
    # Chrome
    "chrome99",
    "chrome100",
    "chrome101",
    "chrome104",
    "chrome107",
    "chrome110",
    "chrome116",
    "chrome119",
    "chrome120",
    "chrome123",
    # "chrome124",
    "chrome99_android",
    # Safari
    "safari15_3",
    "safari15_5",
    "safari17_0",
    "safari17_2_ios",
    # alias
    # "chrome",
    "edge",
    "safari",
    "safari_ios",
    "chrome_android",
]


def test_curl_browser():
    session = requests.Session()
    useable_browsers = []
    for browser in BROWSER_TYPES:
        try:
            session.impersonate = browser
            session.cookies.set('selected_country_code', 'CN')
            session.cookies.set('selected_culture_code', 'zh_CN')
            res = session.get('https://www.eastarjet.com/newstar/PGWHC00001?lang=CN', timeout=5)
            if res.status_code == 200:
                session_id = res.text.split('// ey')[-1]
                session_id = 'ey' + session_id.split('''\n        \t\t//''')[0]
                session.cookies.update(dict(res.cookies))
                logger.debug(session.cookies.get_dict())
                logger.debug(session_id)
                if 'JSESSIONID' in session.cookies.get_dict() and session_id in res.text:
                    useable_browsers.append(browser)
        except Exception as e:
            logger.exception(e)
    logger.info(f'useable browsers: {useable_browsers}')
    unuseable_browsers = list(set(BROWSER_TYPES) - set(useable_browsers))
    logger.info(f'unuseable browsers: {unuseable_browsers}')
