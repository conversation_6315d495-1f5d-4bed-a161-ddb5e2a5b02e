#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试后退操作的完整流程
"""

import sys
import os
sys.path.append('/home/<USER>/workspace/flight/vj_crawler')

from unittest.mock import Mock, patch, MagicMock
from app.services.agent_service import AgentService


def test_back_operation_flow():
    """测试后退操作的完整流程"""
    print("=== 测试后退操作的完整流程 ===")
    
    # 创建AgentService实例
    service = AgentService(username='test', password='test', airline_code='VJ')
    
    # Mock client
    service.client = Mock()
    service.client.travel_options = Mock()
    service.client.details = Mock()
    
    # 模拟数据
    new_fare_key = '8,DLX_FARE_KEY'
    passengers = [
        {
            'passenger_type': 'adult',
            'first_name': 'John',
            'last_name': 'Doe'
        }
    ]
    
    # Mock返回值
    service.client.travel_options.return_value = 'mocked_travel_options_response'
    service.client.details.return_value = '预订页面 - 座位安置'
    
    # Mock set_proxy方法
    service.set_proxy = Mock()
    
    print("测试1: 后退到travel_options页面")
    
    # 调用_book_step3_switch_to_20kg方法
    result = service._book_step3_switch_to_20kg(new_fare_key, passengers)
    
    # 验证travel_options被调用了3次
    assert service.client.travel_options.call_count == 2, f"期望调用2次，实际{service.client.travel_options.call_count}"
    
    # 验证第一次调用是后退操作
    first_call = service.client.travel_options.call_args_list[0]
    print(f"第一次调用参数: {first_call}")
    
    # 检查第一次调用的参数
    args, kwargs = first_call
    assert 'fare_key' in kwargs and kwargs['fare_key'] == new_fare_key
    assert 'button' in kwargs and kwargs['button'] == 'back'
    print("✅ 后退操作参数正确")
    
    # 验证第二次调用是重新选择
    second_call = service.client.travel_options.call_args_list[1]
    print(f"第二次调用参数: {second_call}")
    
    args, kwargs = second_call
    assert 'fare_key' in kwargs and kwargs['fare_key'] == new_fare_key
    # 第二次调用应该使用默认的continue
    assert 'button' not in kwargs or kwargs.get('button') == 'continue'
    print("✅ 重新选择操作参数正确")
    
    # 验证details被调用
    assert service.client.details.call_count == 1, f"期望调用1次，实际{service.client.details.call_count}"
    details_call = service.client.details.call_args_list[0]
    args, kwargs = details_call
    assert 'passengers' in kwargs and kwargs['passengers'] == passengers
    print("✅ 重新填写乘客信息正确")
    
    print("✅ 后退操作完整流程测试通过")


def test_back_operation_with_error():
    """测试后退操作遇到错误的情况"""
    print("\n=== 测试后退操作遇到错误的情况 ===")
    
    service = AgentService(username='test', password='test', airline_code='VJ')
    service.client = Mock()
    service.client.travel_options = Mock()
    service.client.details = Mock()
    service.set_proxy = Mock()
    
    # 模拟数据
    new_fare_key = '8,DLX_FARE_KEY'
    passengers = [{'passenger_type': 'adult', 'first_name': 'John', 'last_name': 'Doe'}]
    
    # Mock返回值 - details返回错误
    service.client.travel_options.return_value = 'mocked_travel_options_response'
    service.client.details.return_value = '错误页面'
    
    # Mock agent_helper.get_error_msg
    with patch('app.services.agent_helper.get_error_msg') as mock_get_error_msg:
        mock_get_error_msg.return_value = '测试错误信息'
        
        try:
            result = service._book_step3_switch_to_20kg(new_fare_key, passengers)
            assert False, "应该抛出异常"
        except Exception as e:
            print(f"捕获到期望的异常: {str(e)}")
            assert '重新乘客提交失败' in str(e)
            print("✅ 错误处理测试通过")


def test_price_comparison_logic():
    """测试价格对比逻辑"""
    print("\n=== 测试价格对比逻辑 ===")
    
    service = AgentService(username='test', password='test', airline_code='VJ')
    
    # 模拟数据
    details_2 = 'mocked_html_content'
    passengers = [
        {
            'passenger_type': 'adult',
            'baggages': [
                {
                    'aux_type': 'baggage',
                    'weight': 20,
                    'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1'
                }
            ]
        }
    ]
    
    all_fares = {
        "0kg": {
            "adult": {"base": 100.0, "tax": 20.0},
            "fare_key": "10,ECO_FARE_KEY",
        },
        "20kg": {
            "adult": {"base": 150.0, "tax": 30.0},
            "fare_key": "8,DLX_FARE_KEY",
        }
    }
    
    # Mock相关方法
    with patch('app.services.agent_service.should_use_dlx_cabin') as mock_should_use_dlx:
        with patch('app.services.agent_helper.update_baggage_options') as mock_update_baggage:
            with patch('copy.deepcopy') as mock_deepcopy:
                
                mock_should_use_dlx.return_value = True
                mock_deepcopy.return_value = passengers
                mock_update_baggage.return_value = passengers
                
                # 测试不应该切换的情况（20kg价格更高）
                should_switch, new_fare_key = service._book_step2_check_dlx_switch(
                    details_2, passengers, False, all_fares
                )
                
                print(f"价格对比结果: should_switch={should_switch}, new_fare_key={new_fare_key}")
                
                # 计算期望结果
                eco_total = 100.0 + 20.0 + 55.3  # 175.3
                dlx_total = 150.0 + 30.0  # 180.0
                print(f"ECO+行李: {eco_total}, DLX: {dlx_total}")
                
                # 180.0 > 175.3，所以不应该切换
                assert not should_switch, "不应该切换到20kg舱位"
                assert new_fare_key is None, "new_fare_key应该是None"
                print("✅ 价格对比逻辑测试通过（不切换）")
                
                # 测试应该切换的情况（降低20kg价格）
                all_fares["20kg"]["adult"]["base"] = 120.0  # 降低基础价格
                
                should_switch, new_fare_key = service._book_step2_check_dlx_switch(
                    details_2, passengers, False, all_fares
                )
                
                dlx_total_new = 120.0 + 30.0  # 150.0
                print(f"降低后 - ECO+行李: {eco_total}, DLX: {dlx_total_new}")
                
                # 150.0 < 175.3，所以应该切换
                assert should_switch, "应该切换到20kg舱位"
                assert new_fare_key == "8,DLX_FARE_KEY", f"期望'8,DLX_FARE_KEY'，实际'{new_fare_key}'"
                print("✅ 价格对比逻辑测试通过（切换）")


if __name__ == "__main__":
    print("🚀 开始测试后退操作的完整流程")
    
    try:
        test_back_operation_flow()
        test_back_operation_with_error()
        test_price_comparison_logic()
        
        print("\n🎉 所有测试通过！后退操作实现正确")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
