"""
真实的Book流程集成测试
测试完整的book流程，减少mock，发现真实的代码问题
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from app.services.agent_service import should_use_dlx_cabin


class TestBookIntegrationReal:
    """真实的Book流程集成测试"""

    def test_book_with_real_baggage_weight_conversion(self):
        """测试真实的行李重量转换问题"""
        # Arrange - 模拟真实的乘客数据，包含浮点数重量
        passengers = [
            {
                'passenger_type': 'adult',
                'first_name': '<PERSON>',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20.0,  # 浮点数重量
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            }
        ]

        # 模拟真实的all_fares数据结构
        all_fares = {
            "0kg": {
                "adult": {"base": 100.0, "tax": 20.0, "quantity": 10},
                "child": {"base": 80.0, "tax": 20.0, "quantity": 10},
                "infant": {"base": 10.0, "tax": 5.0, "quantity": 10},
                "fare_key": "10,ECO_FARE_KEY",
            },
            "20kg": {
                "adult": {"base": 120.0, "tax": 30.0, "quantity": 5},
                "child": {"base": 100.0, "tax": 30.0, "quantity": 5},
                "infant": {"base": 30.0, "tax": 10.0, "quantity": 5},
                "fare_key": "8,DLX_FARE_KEY",
            },
        }

        # Mock必要的外部依赖，但不过度mock内部逻辑
        with patch.object(self.agent_service, '_book_step1_add_passengers') as mock_step1, patch.object(
            self.agent_service, '_book_step4_complete_booking'
        ) as mock_step4, patch.object(self.agent_service, 'driver') as mock_driver:

            # 设置mock返回值
            mock_step1.return_value = True
            mock_step4.return_value = {"success": True, "order_id": "12345"}
            mock_driver.current_url = "https://test.com"

            # Act - 调用真实的book方法
            result = self.agent_service.book(passengers, all_fares)

            # Assert - 验证结果
            assert result is not None
            # 验证step1被调用
            mock_step1.assert_called_once()

    def test_book_detects_baggage_weight_mismatch(self):
        """测试book方法能检测到浮点数和整数key不匹配的问题"""
        # Arrange - 创建会导致key不匹配的场景
        passengers = [
            {
                'passenger_type': 'adult',
                'first_name': 'John',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20.5,  # 非整数重量
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            }
        ]

        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}},  # 注意：这里是"20kg"，不是"20.5kg"
        }

        # Mock外部依赖
        with patch.object(self.agent_service, '_book_step1_add_passengers') as mock_step1:
            mock_step1.return_value = True

            # Act & Assert - 应该能正确处理浮点数转整数
            # 这个测试会暴露真实的代码问题
            try:
                result = self.agent_service.book(passengers, all_fares)
                # 如果代码正确处理了浮点数转换，应该能找到"20kg"舱位
                assert result is not None
            except KeyError as e:
                # 如果抛出KeyError，说明代码没有正确处理浮点数转换
                pytest.fail(f"代码没有正确处理浮点数转换: {e}")

    def test_should_use_dlx_cabin_with_real_data_structure(self):
        """测试should_use_dlx_cabin函数处理真实数据结构"""
        # Arrange - 使用真实的数据结构
        passengers = [
            {
                'passenger_type': 'adult',
                'first_name': 'John',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20.0,  # 浮点数
                        'count': 1,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            }
        ]

        # 真实的all_fares结构
        all_fares = {
            "0kg": {"adult": {"base": 100.0, "tax": 20.0, "quantity": 10}, "fare_key": "10,ECO_FARE_KEY"},
            "20kg": {  # 注意：key是字符串"20kg"，不是"20.0kg"
                "adult": {"base": 120.0, "tax": 30.0, "quantity": 5},
                "fare_key": "8,DLX_FARE_KEY",
            },
        }

        # Act - 直接调用函数，不mock内部逻辑
        result = self.agent_service.should_use_dlx_cabin(passengers, all_fares)

        # Assert - 验证结果
        assert result is True  # 应该能正确匹配"20kg"舱位

    def test_extract_baggage_weight_handles_float(self):
        """测试提取行李重量时处理浮点数"""
        # Arrange
        passengers = [{'passenger_type': 'adult', 'baggages': [{'aux_type': 'baggage', 'weight': 25.5}]}]  # 浮点数重量

        # Act - 调用真实的方法
        baggage_weight = self.agent_service._extract_baggage_weight(passengers)

        # Assert
        assert baggage_weight == 25.5  # 应该返回原始浮点数

    def test_check_dlx_switch_with_float_weight(self):
        """测试DLX切换检查时处理浮点数重量"""
        # Arrange
        passengers = [{'passenger_type': 'adult', 'baggages': [{'aux_type': 'baggage', 'weight': 20.0}]}]  # 浮点数

        all_fares = {"0kg": {"adult": {"quantity": 10}}, "20kg": {"adult": {"quantity": 5}}}  # 整数key

        # Act - 调用真实的方法
        should_switch, target_kg_key = self.agent_service._check_dlx_switch(passengers, all_fares)

        # Assert
        assert should_switch is True
        assert target_kg_key == "20kg"  # 应该正确转换为整数key

    def test_book_step2_integration(self):
        """测试book step2的集成逻辑"""
        # Arrange
        passengers = [{'passenger_type': 'adult', 'baggages': [{'aux_type': 'baggage', 'weight': 20.0}]}]

        all_fares = {"0kg": {"adult": {"quantity": 10}}, "20kg": {"adult": {"quantity": 5}}}

        # Mock driver但不mock内部逻辑
        with patch.object(self.agent_service, 'driver') as mock_driver:
            mock_driver.current_url = "https://test.com"

            # Act
            result = self.agent_service._book_step2_check_dlx_switch(passengers, all_fares)

            # Assert
            assert result is not None
