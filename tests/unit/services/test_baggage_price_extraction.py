#!/usr/bin/env python3
"""
行李价格提取功能单元测试
测试agent_helper中的行李价格提取功能
"""

from app.services.agent_helper import extract_baggage_price_from_select_value


class TestBaggagePriceExtraction:
    """测试行李价格提取功能"""

    def test_extract_baggage_price_success(self):
        """测试从select_value中提取行李价格 - 成功场景"""
        # Arrange
        select_value = "1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1"
        expected_price = 55.3
        
        # Act
        result = extract_baggage_price_from_select_value(select_value)
        
        # Assert
        assert result == expected_price

    def test_extract_baggage_price_different_amount(self):
        """测试从select_value中提取行李价格 - 不同金额"""
        # Arrange
        select_value = "1|NA|3|68189641|120.5|False|3|Bag 25kgs||NA||0|Baggage 25kgs|120.50 CNY|120.5|4.42|0|0|1"
        expected_price = 120.5
        
        # Act
        result = extract_baggage_price_from_select_value(select_value)
        
        # Assert
        assert result == expected_price

    def test_extract_baggage_price_zero_amount(self):
        """测试从select_value中提取行李价格 - 零金额"""
        # Arrange
        select_value = "1|NA|3|68189641|0.0|False|3|Bag 0kgs||NA||0|Baggage 0kgs|0.00 CNY|0.0|4.42|0|0|1"
        expected_price = 0.0
        
        # Act
        result = extract_baggage_price_from_select_value(select_value)
        
        # Assert
        assert result == expected_price

    def test_extract_baggage_price_invalid_format(self):
        """测试从select_value中提取行李价格 - 无效格式"""
        # Arrange
        select_value = "invalid|format"
        expected_price = 0.0
        
        # Act
        result = extract_baggage_price_from_select_value(select_value)
        
        # Assert
        assert result == expected_price

    def test_extract_baggage_price_empty_string(self):
        """测试从select_value中提取行李价格 - 空字符串"""
        # Arrange
        select_value = ""
        expected_price = 0.0
        
        # Act
        result = extract_baggage_price_from_select_value(select_value)
        
        # Assert
        assert result == expected_price

    def test_extract_baggage_price_none_input(self):
        """测试从select_value中提取行李价格 - None输入"""
        # Arrange
        select_value = None
        expected_price = 0.0
        
        # Act
        result = extract_baggage_price_from_select_value(select_value)
        
        # Assert
        assert result == expected_price

    def test_extract_baggage_price_insufficient_parts(self):
        """测试从select_value中提取行李价格 - 分段不足"""
        # Arrange
        select_value = "1|NA|3"  # 只有3个分段，不足以提取价格
        expected_price = 0.0
        
        # Act
        result = extract_baggage_price_from_select_value(select_value)
        
        # Assert
        assert result == expected_price

    def test_extract_baggage_price_non_numeric_price(self):
        """测试从select_value中提取行李价格 - 非数字价格"""
        # Arrange
        select_value = "1|NA|3|68189641|abc|False|3|Bag 20kgs||NA||0|Baggage 20kgs|abc CNY|abc|4.42|0|0|1"
        expected_price = 0.0
        
        # Act
        result = extract_baggage_price_from_select_value(select_value)
        
        # Assert
        assert result == expected_price

    def test_extract_baggage_price_float_precision(self):
        """测试从select_value中提取行李价格 - 浮点精度"""
        # Arrange
        select_value = "1|NA|3|68189641|99.99|False|3|Bag 30kgs||NA||0|Baggage 30kgs|99.99 CNY|99.99|4.42|0|0|1"
        expected_price = 99.99
        
        # Act
        result = extract_baggage_price_from_select_value(select_value)
        
        # Assert
        assert result == expected_price

    def test_extract_baggage_price_large_amount(self):
        """测试从select_value中提取行李价格 - 大金额"""
        # Arrange
        select_value = "1|NA|3|68189641|999.99|False|3|Bag 50kgs||NA||0|Baggage 50kgs|999.99 CNY|999.99|4.42|0|0|1"
        expected_price = 999.99
        
        # Act
        result = extract_baggage_price_from_select_value(select_value)
        
        # Assert
        assert result == expected_price


class TestAllFaresStructure:
    """测试all_fares数据结构"""

    def test_all_fares_structure_validation(self):
        """测试all_fares数据结构验证"""
        # Arrange
        all_fares = {
            "0kg": {
                "adult": {"base": 100.0, "tax": 20.0},
                "child": {"base": 80.0, "tax": 16.0},
                "infant": {"base": 0.0, "tax": 0.0},
                "fare_key": "10,ECO_FARE_KEY",
                "cabin": {"cabin_class": "Eco", "code": "ECO"}
            },
            "20kg": {
                "adult": {"base": 150.0, "tax": 30.0},
                "child": {"base": 120.0, "tax": 24.0},
                "infant": {"base": 0.0, "tax": 0.0},
                "fare_key": "8,DLX_FARE_KEY",
                "cabin": {"cabin_class": "Deluxe", "code": "DLX"}
            }
        }
        
        # Act & Assert
        assert "0kg" in all_fares
        assert "20kg" in all_fares
        assert all_fares["0kg"]["cabin"]["code"] == "ECO"
        assert all_fares["20kg"]["cabin"]["code"] == "DLX"
        assert all_fares["0kg"]["fare_key"] == "10,ECO_FARE_KEY"
        assert all_fares["20kg"]["fare_key"] == "8,DLX_FARE_KEY"

    def test_price_comparison_logic_should_switch(self):
        """测试价格对比逻辑 - 应该切换到20kg舱位"""
        # Arrange
        all_fares = {
            "0kg": {"adult": {"base": 100.0, "tax": 20.0}},
            "20kg": {"adult": {"base": 120.0, "tax": 30.0}}  # 150总价
        }
        baggage_price = 55.3
        
        # Act
        eco_total = all_fares["0kg"]["adult"]["base"] + all_fares["0kg"]["adult"]["tax"]
        eco_with_baggage = eco_total + baggage_price  # 175.3
        dlx_total = all_fares["20kg"]["adult"]["base"] + all_fares["20kg"]["adult"]["tax"]  # 150
        should_switch = dlx_total < eco_with_baggage
        
        # Assert
        assert should_switch is True
        assert dlx_total == 150.0
        assert eco_with_baggage == 175.3

    def test_price_comparison_logic_should_not_switch(self):
        """测试价格对比逻辑 - 不应该切换到20kg舱位"""
        # Arrange
        all_fares = {
            "0kg": {"adult": {"base": 100.0, "tax": 20.0}},
            "20kg": {"adult": {"base": 160.0, "tax": 30.0}}  # 190总价
        }
        baggage_price = 55.3
        
        # Act
        eco_total = all_fares["0kg"]["adult"]["base"] + all_fares["0kg"]["adult"]["tax"]
        eco_with_baggage = eco_total + baggage_price  # 175.3
        dlx_total = all_fares["20kg"]["adult"]["base"] + all_fares["20kg"]["adult"]["tax"]  # 190
        should_switch = dlx_total < eco_with_baggage
        
        # Assert
        assert should_switch is False
        assert dlx_total == 190.0
        assert eco_with_baggage == 175.3
