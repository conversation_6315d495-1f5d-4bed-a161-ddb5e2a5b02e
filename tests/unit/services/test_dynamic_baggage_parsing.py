#!/usr/bin/env python3
"""
动态行李解析功能单元测试
测试从HTML中解析免费托运公斤数，并使用动态公斤数构建all_fares
"""

from bs4 import BeautifulSoup

from app.services.agent_helper import extract_free_checked_baggage_weight, parse_flight
from app.services.agent_service import should_use_dlx_cabin


class TestDynamicBaggageParsing:
    """测试动态行李解析功能"""

    def test_extract_free_checked_baggage_weight_eco(self):
        """测试提取ECO舱位免费托运行李重量 - 无免费托运"""
        # Arrange - 模拟ECO舱位HTML
        html_content = '''
        <div class="fareRulesMOver" id="ecofarerules" familyid="1">
            <div class="fareRulesContent">
                <ul>
                    <li class="greencheckmark">7 公斤手提行李</li>
                </ul>
            </div>
            <div class="fareRulesContent">
                <ul>
                    <li class="redx">托运行李（可选）</li>
                </ul>
            </div>
        </div>
        '''
        root_dom = BeautifulSoup(html_content, 'html.parser')

        # Act
        weight = extract_free_checked_baggage_weight(root_dom, "Eco")

        # Assert
        assert weight == 0  # ECO舱位无免费托运行李

    def test_extract_free_checked_baggage_weight_deluxe_chinese_with_or(self):
        """测试提取DELUXE舱位免费托运行李重量 - 中文描述包含"或"字（应该跳过）"""
        # Arrange - 模拟DELUXE舱位HTML（中文包含"或"）
        html_content = '''
        <div class="fareRulesMOver" id="deluxefarerules" familyid="2">
            <div class="fareRulesContent">
                <ul>
                    <li class="greencheckmark">根据航线适用 7 公斤或 10 公斤手提行李</li>
                    <li class="greencheckmark">根据航线免费20 公斤或 40 公斤的托运行李</li>
                </ul>
            </div>
        </div>
        '''
        root_dom = BeautifulSoup(html_content, 'html.parser')

        # Act
        weight = extract_free_checked_baggage_weight(root_dom, "Deluxe")

        # Assert
        assert weight == 20  # 现在取中文描述中的第一个公斤数，不再跳过"或"字

    def test_extract_free_checked_baggage_weight_deluxe_chinese_no_or(self):
        """测试提取DELUXE舱位免费托运行李重量 - 中文描述不包含"或"字"""
        # Arrange - 模拟DELUXE舱位HTML（中文不包含"或"）
        html_content = '''
        <div class="fareRulesMOver" id="deluxefarerules" familyid="2">
            <div class="fareRulesContent">
                <ul>
                    <li class="greencheckmark">免费20公斤的托运行李</li>
                </ul>
            </div>
        </div>
        '''
        root_dom = BeautifulSoup(html_content, 'html.parser')

        # Act
        weight = extract_free_checked_baggage_weight(root_dom, "Deluxe")

        # Assert
        assert weight == 20  # 不包含"或"字的中文描述可以正确解析

    def test_extract_free_checked_baggage_weight_deluxe_english(self):
        """测试提取DELUXE舱位免费托运行李重量 - 英文描述"""
        # Arrange - 模拟DELUXE舱位HTML（英文）
        html_content = '''
        <div class="fareRulesMOver" id="deluxefarerules" familyid="2">
            <div class="fareRulesContent">
                <ul>
                    <li class="greencheckmark">7kgs carry on baggage.</li>
                    <li class="greencheckmark">20kgs checked baggage.</li>
                </ul>
            </div>
        </div>
        '''
        root_dom = BeautifulSoup(html_content, 'html.parser')

        # Act
        weight = extract_free_checked_baggage_weight(root_dom, "Deluxe")

        # Assert
        assert weight == 20  # 英文格式也能正确提取

    def test_extract_free_checked_baggage_weight_english_priority(self):
        """测试英文描述优先级 - 同时存在中英文时优先使用英文"""
        # Arrange - 模拟同时包含中英文描述的HTML
        html_content = '''
        <div class="fareRulesMOver" id="deluxefarerules" familyid="2">
            <div class="fareRulesContent">
                <ul>
                    <li class="greencheckmark">根据航线免费20 公斤或 40 公斤的托运行李</li>
                    <li class="greencheckmark">25kgs checked baggage.</li>
                </ul>
            </div>
        </div>
        '''
        root_dom = BeautifulSoup(html_content, 'html.parser')

        # Act
        weight = extract_free_checked_baggage_weight(root_dom, "Deluxe")

        # Assert
        assert weight == 25  # 应该优先使用英文描述的25kg，而不是中文的20kg

    def test_extract_free_checked_baggage_weight_skyboss(self):
        """测试提取SKYBOSS舱位免费托运行李重量"""
        # Arrange - 模拟SKYBOSS舱位HTML（包含英文标签）
        html_content = '''
        <div class="fareRulesMOver" id="skybossfarerules" familyid="6">
            <div class="fareRulesContent">
                <ul>
                    <li class="greencheckmark">30kgs checked baggage.</li>
                    <li class="greencheckmark">根据航线免费30 公斤或 50 公斤的托运行李</li>
                </ul>
            </div>
        </div>
        '''
        root_dom = BeautifulSoup(html_content, 'html.parser')

        # Act
        weight = extract_free_checked_baggage_weight(root_dom, "SkyBoss")

        # Assert
        assert weight == 30  # SKYBOSS舱位30kg

    def test_extract_free_checked_baggage_weight_business(self):
        """测试提取BUSINESS舱位免费托运行李重量"""
        # Arrange - 模拟BUSINESS舱位HTML（包含英文标签）
        html_content = '''
        <div class="fareRulesMOver" id="skybosspremierfarerules" familyid="7">
            <div class="fareRulesContent">
                <ul>
                    <li class="greencheckmark">40kgs checked baggage.</li>
                    <li class="greencheckmark">根据航线免费40 公斤或 60 公斤的托运行李</li>
                </ul>
            </div>
        </div>
        '''
        root_dom = BeautifulSoup(html_content, 'html.parser')

        # Act
        weight = extract_free_checked_baggage_weight(root_dom, "Business")

        # Assert
        assert weight == 40  # BUSINESS舱位40kg

    def test_extract_free_checked_baggage_weight_not_found(self):
        """测试提取免费托运行李重量 - 舱位不存在"""
        # Arrange - 空HTML
        html_content = '<div></div>'
        root_dom = BeautifulSoup(html_content, 'html.parser')

        # Act
        weight = extract_free_checked_baggage_weight(root_dom, "NonExistent")

        # Assert
        assert weight == 0  # 舱位不存在返回0


class TestDynamicShouldUseDlxCabin:
    """测试动态should_use_dlx_cabin函数"""

    def test_should_use_dlx_cabin_consistent_20kg(self):
        """测试所有乘客购买20kg行李且存在20kg舱位"""
        # Arrange
        passengers = [
            {'passenger_type': 'adult', 'baggages': [{'aux_type': 'baggage', 'weight': 20}]},
            {'passenger_type': 'adult', 'baggages': [{'aux_type': 'baggage', 'weight': 20}]},
        ]

        all_fares = {
            "0kg": {"adult": {"quantity": 10}, "fare_key": "1,ECO_FARE"},
            "20kg": {"adult": {"quantity": 5}, "fare_key": "2,DLX_FARE"},
        }

        # Act
        result = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert result is True  # 所有乘客20kg行李，且20kg舱位存在

    def test_should_use_dlx_cabin_consistent_30kg(self):
        """测试所有乘客购买30kg行李且存在30kg舱位"""
        # Arrange
        passengers = [{'passenger_type': 'adult', 'baggages': [{'aux_type': 'baggage', 'weight': 30}]}]

        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}},
            "30kg": {"adult": {"quantity": 3}},  # SKYBOSS舱位
        }

        # Act
        result = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert result is True  # 30kg行李对应30kg舱位

    def test_should_use_dlx_cabin_inconsistent_weights(self):
        """测试乘客购买不同重量行李"""
        # Arrange
        passengers = [
            {'passenger_type': 'adult', 'baggages': [{'aux_type': 'baggage', 'weight': 20}]},
            {'passenger_type': 'adult', 'baggages': [{'aux_type': 'baggage', 'weight': 30}]},  # 不同重量
        ]

        all_fares = {"20kg": {"adult": {"quantity": 5}}, "30kg": {"adult": {"quantity": 3}}}

        # Act
        result = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert result is False  # 行李重量不一致

    def test_should_use_dlx_cabin_no_matching_cabin(self):
        """测试行李重量一致但无对应舱位"""
        # Arrange
        passengers = [{'passenger_type': 'adult', 'baggages': [{'aux_type': 'baggage', 'weight': 25}]}]  # 25kg行李

        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}},
            "30kg": {"adult": {"quantity": 3}},
            # 没有25kg舱位
        }

        # Act
        result = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert result is False  # 无对应舱位

    def test_should_use_dlx_cabin_insufficient_tickets(self):
        """测试余票不足"""
        # Arrange
        passengers = [
            {'passenger_type': 'adult', 'baggages': [{'aux_type': 'baggage', 'weight': 20}]},
            {'passenger_type': 'adult', 'baggages': [{'aux_type': 'baggage', 'weight': 20}]},
        ]

        all_fares = {"20kg": {"adult": {"quantity": 1}, "fare_key": "2,DLX_FARE"}}  # 只有1张票，需要2张

        # Act
        result = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert result is False  # 余票不足

    def test_should_use_dlx_cabin_no_baggage(self):
        """测试无行李情况"""
        # Arrange
        passengers = [{'passenger_type': 'adult', 'baggages': []}]  # 无行李

        all_fares = {"0kg": {"adult": {"quantity": 10}}, "20kg": {"adult": {"quantity": 5}}}

        # Act
        result = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert result is False  # 无行李不符合条件

    def test_should_use_dlx_cabin_infant_not_counted(self):
        """测试婴儿不计入余票需求"""
        # Arrange
        passengers = [
            {'passenger_type': 'adult', 'baggages': [{'aux_type': 'baggage', 'weight': 20}]},
            {'passenger_type': 'infant', 'baggages': [{'aux_type': 'baggage', 'weight': 20}]},
        ]

        all_fares = {"20kg": {"adult": {"quantity": 1}, "fare_key": "2,DLX_FARE"}}  # 1张票，婴儿不占座

        # Act
        result = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert result is True  # 婴儿不占座，1张票足够


class TestRealHtmlParsing:
    """使用真实HTML文件测试解析功能"""

    def test_parse_real_html_file(self):
        """测试使用真实HTML文件解析免费托运公斤数"""
        # Arrange - 读取真实HTML文件
        with open('tests/fixtures/html/agent_search_result.html', 'r', encoding='utf-8') as f:
            html_content = f.read()

        root_dom = BeautifulSoup(html_content, 'html.parser')

        # Act - 测试各个舱位的免费托运公斤数
        eco_weight = extract_free_checked_baggage_weight(root_dom, "Eco")
        deluxe_weight = extract_free_checked_baggage_weight(root_dom, "Deluxe")
        skyboss_weight = extract_free_checked_baggage_weight(root_dom, "SkyBoss")
        business_weight = extract_free_checked_baggage_weight(root_dom, "Business")

        # Assert - 验证解析结果
        assert eco_weight == 0  # ECO舱位无免费托运
        assert deluxe_weight == 20  # DELUXE舱位20kg
        assert skyboss_weight == 30  # 现在取中文描述中的第一个公斤数，不再跳过"或"字
        assert business_weight == 40  # 现在取中文描述中的第一个公斤数，不再跳过"或"字

    def test_all_fares_structure_with_real_data(self):
        """测试使用真实数据构建all_fares结构"""
        # Arrange - 模拟解析后的舱位信息
        all_fares = {
            "0kg": {  # ECO舱位
                "adult": {"base": 100.0, "tax": 20.0, "total": 120.0, "quantity": 10},
                "fare_key": "10,ECO_FARE",
                "cabin": {"cabin_class": "Eco", "code": "ECO"},
                "free_checked_baggage_kg": 0,
            },
            "20kg": {  # DELUXE舱位
                "adult": {"base": 150.0, "tax": 30.0, "total": 180.0, "quantity": 5},
                "fare_key": "5,DLX_FARE",
                "cabin": {"cabin_class": "Deluxe", "code": "DLX"},
                "free_checked_baggage_kg": 20,
            },
            "30kg": {  # SKYBOSS舱位
                "adult": {"base": 200.0, "tax": 40.0, "total": 240.0, "quantity": 3},
                "fare_key": "3,SKY_FARE",
                "cabin": {"cabin_class": "SkyBoss", "code": "SKY"},
                "free_checked_baggage_kg": 30,
            },
            "40kg": {  # BUSINESS舱位
                "adult": {"base": 300.0, "tax": 60.0, "total": 360.0, "quantity": 2},
                "fare_key": "2,BUS_FARE",
                "cabin": {"cabin_class": "Business", "code": "BUS"},
                "free_checked_baggage_kg": 40,
            },
        }

        # Act & Assert - 验证all_fares结构
        assert "0kg" in all_fares
        assert "20kg" in all_fares
        assert "30kg" in all_fares
        assert "40kg" in all_fares

        # 验证每个舱位的免费托运公斤数
        assert all_fares["0kg"]["free_checked_baggage_kg"] == 0
        assert all_fares["20kg"]["free_checked_baggage_kg"] == 20
        assert all_fares["30kg"]["free_checked_baggage_kg"] == 30
        assert all_fares["40kg"]["free_checked_baggage_kg"] == 40

        # 验证舱位代码
        assert all_fares["0kg"]["cabin"]["code"] == "ECO"
        assert all_fares["20kg"]["cabin"]["code"] == "DLX"
        assert all_fares["30kg"]["cabin"]["code"] == "SKY"
        assert all_fares["40kg"]["cabin"]["code"] == "BUS"


class TestDynamicBookingFlow:
    """测试动态预订流程"""

    def test_dynamic_booking_flow_integration(self):
        """测试动态预订流程的集成"""
        # Arrange - 模拟完整的all_fares数据
        all_fares = {
            "0kg": {  # ECO舱位
                "adult": {"base": 100.0, "tax": 20.0, "total": 120.0, "quantity": 10},
                "child": {"base": 100.0, "tax": 20.0, "total": 120.0, "quantity": 10},
                "infant": {"base": 0.0, "tax": 0.0, "total": 0.0, "quantity": 0},
                "fare_key": "10,ECO_FARE",
                "cabin": {"cabin_class": "Eco", "code": "ECO"},
                "free_checked_baggage_kg": 0,
            },
            "20kg": {  # DELUXE舱位
                "adult": {"base": 150.0, "tax": 30.0, "total": 180.0, "quantity": 5},
                "child": {"base": 150.0, "tax": 30.0, "total": 180.0, "quantity": 5},
                "infant": {"base": 0.0, "tax": 0.0, "total": 0.0, "quantity": 0},
                "fare_key": "5,DLX_FARE",
                "cabin": {"cabin_class": "Deluxe", "code": "DLX"},
                "free_checked_baggage_kg": 20,
            },
            "30kg": {  # SKYBOSS舱位
                "adult": {"base": 200.0, "tax": 40.0, "total": 240.0, "quantity": 3},
                "child": {"base": 200.0, "tax": 40.0, "total": 240.0, "quantity": 3},
                "infant": {"base": 0.0, "tax": 0.0, "total": 0.0, "quantity": 0},
                "fare_key": "3,SKY_FARE",
                "cabin": {"cabin_class": "SkyBoss", "code": "SKY"},
                "free_checked_baggage_kg": 30,
            },
        }

        # 测试场景1：所有乘客购买20kg行李，应该切换到20kg舱位
        passengers_20kg = [
            {'passenger_type': 'adult', 'baggages': [{'aux_type': 'baggage', 'weight': 20}]},
            {'passenger_type': 'adult', 'baggages': [{'aux_type': 'baggage', 'weight': 20}]},
        ]

        # Act & Assert
        result_20kg = should_use_dlx_cabin(passengers_20kg, all_fares)
        assert result_20kg is True  # 应该使用20kg舱位

        # 测试场景2：所有乘客购买30kg行李，应该切换到30kg舱位
        passengers_30kg = [{'passenger_type': 'adult', 'baggages': [{'aux_type': 'baggage', 'weight': 30}]}]

        result_30kg = should_use_dlx_cabin(passengers_30kg, all_fares)
        assert result_30kg is True  # 应该使用30kg舱位

        # 测试场景3：乘客购买25kg行李，但没有25kg舱位，不应该切换
        passengers_25kg = [{'passenger_type': 'adult', 'baggages': [{'aux_type': 'baggage', 'weight': 25}]}]

        result_25kg = should_use_dlx_cabin(passengers_25kg, all_fares)
        assert result_25kg is False  # 没有对应舱位，不切换

        # 测试场景4：验证all_fares结构的完整性
        for kg_key, fare_data in all_fares.items():
            # 验证必要字段存在
            assert "adult" in fare_data
            assert "child" in fare_data
            assert "infant" in fare_data
            assert "fare_key" in fare_data
            assert "cabin" in fare_data
            assert "free_checked_baggage_kg" in fare_data

            # 验证免费托运公斤数与key一致
            expected_kg = int(kg_key.replace('kg', ''))
            assert fare_data["free_checked_baggage_kg"] == expected_kg

    def test_price_comparison_logic(self):
        """测试价格比较逻辑"""
        # Arrange - 模拟价格场景
        all_fares = {
            "0kg": {"adult": {"base": 100.0, "tax": 20.0, "total": 120.0, "quantity": 10}, "fare_key": "10,ECO_FARE"},
            "20kg": {
                "adult": {"base": 140.0, "tax": 25.0, "total": 165.0, "quantity": 5},  # 比ECO+行李费便宜
                "fare_key": "5,DLX_FARE",
            },
        }

        # 模拟ECO舱位+20kg行李费 = 120 + 60 = 180
        # DLX舱位(包含20kg) = 165
        # 所以DLX更便宜，应该切换

        eco_base_tax = all_fares["0kg"]["adult"]["base"] + all_fares["0kg"]["adult"]["tax"]  # 120
        dlx_total = all_fares["20kg"]["adult"]["total"]  # 165
        baggage_fee = 60  # 假设20kg行李费

        # Act & Assert
        assert dlx_total < (eco_base_tax + baggage_fee)  # 165 < 180，DLX更便宜

        # 验证价格比较逻辑
        eco_plus_baggage = eco_base_tax + baggage_fee  # 180
        savings = eco_plus_baggage - dlx_total  # 15
        assert savings == 15  # 使用DLX可以节省15
