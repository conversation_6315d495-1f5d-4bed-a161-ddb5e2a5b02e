#!/usr/bin/env python3
"""
DLX舱位切换实际业务场景测试
测试无行李、有行李符合切换、有行李不符合切换等实际场景
"""

from app.services.agent_helper import extract_baggage_price_from_select_value
from app.services.agent_service import should_use_dlx_cabin


class TestRealWorldDLXSwitchingScenarios:
    """测试实际业务场景中的DLX切换逻辑"""

    def test_scenario_no_baggage(self):
        """场景1：无行李 - 不应该切换到DLX舱位"""
        # Arrange - 乘客没有购买行李
        passengers = [{'passenger_type': 'adult', 'first_name': '<PERSON>', 'last_name': 'Doe', 'baggages': []}]  # 无行李

        # Act
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        should_switch = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert should_switch is False  # 无行李不应该切换

    def test_scenario_baggage_should_switch(self):
        """场景2：有行李且符合切换条件 - 应该切换到DLX舱位"""
        # Arrange - 所有乘客都购买20kg行李
        passengers = [
            {
                'passenger_type': 'adult',
                'first_name': 'John',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            }
        ]

        all_fares = {
            "0kg": {"adult": {"base": 100.0, "tax": 20.0}, "fare_key": "10,ECO_FARE_KEY"},  # 120总价
            "20kg": {"adult": {"base": 120.0, "tax": 30.0}, "fare_key": "8,DLX_FARE_KEY"},  # 150总价，低于ECO+行李175.3
        }

        # Act
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        should_switch = should_use_dlx_cabin(passengers, all_fares)

        # 计算价格对比
        baggage_price = extract_baggage_price_from_select_value(passengers[0]['baggages'][0]['from_select_value'])
        eco_total = all_fares["0kg"]["adult"]["base"] + all_fares["0kg"]["adult"]["tax"]
        eco_with_baggage = eco_total + baggage_price
        dlx_total = all_fares["20kg"]["adult"]["base"] + all_fares["20kg"]["adult"]["tax"]

        # Assert
        assert should_switch is True  # 所有乘客都购买20kg行李
        assert baggage_price == 55.3
        assert eco_with_baggage == 175.3
        assert dlx_total == 150.0
        assert dlx_total < eco_with_baggage  # DLX更便宜，符合切换条件

    def test_scenario_baggage_should_not_switch_price_higher(self):
        """场景3：有行李但不符合切换条件 - DLX价格更高"""
        # Arrange - 所有乘客都购买20kg行李，但DLX价格更高
        passengers = [
            {
                'passenger_type': 'adult',
                'first_name': 'John',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|30.0|False|3|Bag 20kgs||NA||0|Baggage 20kgs|30.00 CNY|30.0|4.42|0|0|1',
                    }
                ],
            }
        ]

        all_fares = {
            "0kg": {"adult": {"base": 100.0, "tax": 20.0}, "fare_key": "10,ECO_FARE_KEY"},  # 120总价
            "20kg": {"adult": {"base": 160.0, "tax": 30.0}, "fare_key": "8,DLX_FARE_KEY"},  # 190总价，高于ECO+行李150.0
        }

        # Act
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        should_switch = should_use_dlx_cabin(passengers, all_fares)

        # 计算价格对比
        baggage_price = extract_baggage_price_from_select_value(passengers[0]['baggages'][0]['from_select_value'])
        eco_total = all_fares["0kg"]["adult"]["base"] + all_fares["0kg"]["adult"]["tax"]
        eco_with_baggage = eco_total + baggage_price
        dlx_total = all_fares["20kg"]["adult"]["base"] + all_fares["20kg"]["adult"]["tax"]

        # Assert
        assert should_switch is True  # 所有乘客都购买20kg行李，满足基本条件
        assert baggage_price == 30.0
        assert eco_with_baggage == 150.0
        assert dlx_total == 190.0
        assert dlx_total > eco_with_baggage  # DLX更贵，虽然满足基本条件但价格不优

    def test_scenario_mixed_baggage(self):
        """场景4：混合行李情况 - 部分乘客有行李"""
        # Arrange - 部分乘客购买行李
        passengers = [
            {
                'passenger_type': 'adult',
                'first_name': 'John',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            },
            {'passenger_type': 'adult', 'first_name': 'Jane', 'last_name': 'Doe', 'baggages': []},  # 无行李
        ]

        # Act
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        should_switch = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert should_switch is False  # 不是所有乘客都购买20kg行李

    def test_scenario_different_baggage_weights(self):
        """场景5：不同重量行李 - 非20kg行李"""
        # Arrange - 乘客购买非20kg行李
        passengers = [
            {
                'passenger_type': 'adult',
                'first_name': 'John',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 25,  # 25kg行李，不是20kg
                        'from_select_value': '1|NA|3|68189641|75.5|False|3|Bag 25kgs||NA||0|Baggage 25kgs|75.50 CNY|75.5|4.42|0|0|1',
                    }
                ],
            }
        ]

        # Act
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        should_switch = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert should_switch is False  # 不是20kg标准行李

    def test_scenario_null_baggage_select_value(self):
        """场景6：行李select_value为None的情况"""
        # Arrange - 行李信息不完整
        passengers = [
            {
                'passenger_type': 'adult',
                'first_name': 'John',
                'last_name': 'Doe',
                'baggages': [{'aux_type': 'baggage', 'weight': 20, 'from_select_value': None}],  # None值
            }
        ]

        # Act
        baggage_price = extract_baggage_price_from_select_value(passengers[0]['baggages'][0]['from_select_value'])
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        should_switch = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert baggage_price == 0.0  # None输入返回0.0
        assert should_switch is True  # 仍然满足20kg条件（虽然价格提取失败）

    def test_scenario_multiple_passengers_all_20kg(self):
        """场景7：多乘客都购买20kg行李"""
        # Arrange - 多个乘客都购买20kg行李
        passengers = [
            {
                'passenger_type': 'adult',
                'first_name': 'John',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            },
            {
                'passenger_type': 'adult',
                'first_name': 'Jane',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            },
        ]

        # Act
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        should_switch = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert should_switch is True  # 所有乘客都购买20kg行李

    def test_scenario_child_with_baggage(self):
        """场景8：儿童乘客购买行李"""
        # Arrange - 包含儿童乘客
        passengers = [
            {
                'passenger_type': 'adult',
                'first_name': 'John',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            },
            {
                'passenger_type': 'child',
                'first_name': 'Little',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            },
        ]

        # Act
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        should_switch = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert should_switch is True  # 所有乘客都购买20kg行李

    def test_scenario_infant_no_baggage_current_logic(self):
        """场景9：婴儿乘客无行李 - 当前逻辑要求所有乘客都有行李"""
        # Arrange - 包含婴儿乘客（当前逻辑要求所有乘客都有行李）
        passengers = [
            {
                'passenger_type': 'adult',
                'first_name': 'John',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            },
            {'passenger_type': 'infant', 'first_name': 'Baby', 'last_name': 'Doe', 'baggages': []},  # 婴儿无行李
        ]

        # Act
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        should_switch = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        # 当前业务逻辑：要求所有乘客都有20kg行李，包括婴儿
        # 所以婴儿无行李时不应该切换
        assert should_switch is False  # 婴儿无行李，不符合当前逻辑

    def test_scenario_infant_with_baggage_current_logic(self):
        """场景10：婴儿乘客也有行李 - 符合当前逻辑"""
        # Arrange - 包含婴儿乘客且婴儿也有20kg行李
        passengers = [
            {
                'passenger_type': 'adult',
                'first_name': 'John',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            },
            {
                'passenger_type': 'infant',
                'first_name': 'Baby',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            },
        ]

        # Act
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        should_switch = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        # 当前业务逻辑：所有乘客（包括婴儿）都有20kg行李
        assert should_switch is True  # 所有乘客都有20kg行李，符合当前逻辑

    def test_scenario_insufficient_dlx_tickets(self):
        """场景11：20kg舱位余票不足 - 不应该切换"""
        # Arrange - 所有乘客都购买20kg行李，但DLX余票不足
        passengers = [
            {
                'passenger_type': 'adult',
                'first_name': 'John',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            },
            {
                'passenger_type': 'adult',
                'first_name': 'Jane',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            },
        ]

        all_fares = {
            "0kg": {"adult": {"base": 100.0, "tax": 20.0, "quantity": 10}, "fare_key": "10,ECO_FARE_KEY"},
            "20kg": {
                "adult": {"base": 120.0, "tax": 30.0, "quantity": 1},  # 只有1张票，但需要2张
                "fare_key": "8,DLX_FARE_KEY",
            },
        }

        # Act
        should_switch = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert should_switch is False  # 余票不足，不应该切换

    def test_scenario_sufficient_dlx_tickets(self):
        """场景12：20kg舱位余票充足 - 应该切换"""
        # Arrange - 所有乘客都购买20kg行李，DLX余票充足
        passengers = [
            {
                'passenger_type': 'adult',
                'first_name': 'John',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            },
            {
                'passenger_type': 'adult',
                'first_name': 'Jane',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            },
        ]

        all_fares = {
            "0kg": {"adult": {"base": 100.0, "tax": 20.0, "quantity": 10}, "fare_key": "10,ECO_FARE_KEY"},
            "20kg": {
                "adult": {"base": 120.0, "tax": 30.0, "quantity": 5},  # 5张票，足够2个乘客
                "fare_key": "8,DLX_FARE_KEY",
            },
        }

        # Act
        should_switch = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert should_switch is True  # 余票充足，应该切换

    def test_scenario_infant_not_counted_in_tickets(self):
        """场景13：婴儿不占座，不计入余票需求"""
        # Arrange - 成人+婴儿，婴儿不占座
        passengers = [
            {
                'passenger_type': 'adult',
                'first_name': 'John',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            },
            {
                'passenger_type': 'infant',
                'first_name': 'Baby',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            },
        ]

        all_fares = {
            "0kg": {"adult": {"base": 100.0, "tax": 20.0, "quantity": 10}, "fare_key": "10,ECO_FARE_KEY"},
            "20kg": {
                "adult": {"base": 120.0, "tax": 30.0, "quantity": 1},  # 只有1张票，但婴儿不占座
                "fare_key": "8,DLX_FARE_KEY",
            },
        }

        # Act
        should_switch = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert should_switch is True  # 婴儿不占座，1张票足够成人使用

    def test_scenario_no_all_fares_provided(self):
        """场景14：未提供all_fares信息 - 跳过余票检查"""
        # Arrange - 所有乘客都购买20kg行李，但未提供all_fares
        passengers = [
            {
                'passenger_type': 'adult',
                'first_name': 'John',
                'last_name': 'Doe',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            }
        ]

        # Act - 不提供all_fares参数
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        should_switch = should_use_dlx_cabin(passengers, all_fares)

        # Assert
        assert should_switch is True  # 跳过余票检查，只检查行李条件
