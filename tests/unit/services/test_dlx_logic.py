#!/usr/bin/env python3
"""
DLX舱位逻辑单元测试
"""

import pytest
from app.services.agent_service import should_use_dlx_cabin


class TestDLXLogic:
    """DLX舱位逻辑测试类"""

    def test_should_use_dlx_cabin_all_passengers_with_20kg_baggage(self):
        """测试所有乘客都有20kg行李时应该使用DLX舱位"""
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20.0,
                        'count': 1
                    }
                ]
            },
            {
                'passenger_type': 'child',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20.0,
                        'count': 1
                    }
                ]
            }
        ]
        
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }

        result = should_use_dlx_cabin(passengers, all_fares)
        assert result is True

    def test_should_use_dlx_cabin_partial_passengers_with_baggage(self):
        """测试部分乘客有行李时不应该使用DLX舱位"""
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20.0,
                        'count': 1
                    }
                ]
            },
            {
                'passenger_type': 'child',
                'baggages': []  # 没有行李
            }
        ]
        
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        result = should_use_dlx_cabin(passengers, all_fares)
        assert result is False

    def test_should_use_dlx_cabin_no_passengers_with_baggage(self):
        """测试没有乘客有行李时不应该使用DLX舱位"""
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': []
            },
            {
                'passenger_type': 'child',
                'baggages': []
            }
        ]
        
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        result = should_use_dlx_cabin(passengers, all_fares)
        assert result is False

    def test_should_use_dlx_cabin_wrong_baggage_weight(self):
        """测试行李重量不是20kg时不应该使用DLX舱位"""
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 15.0,  # 不是20kg
                        'count': 1
                    }
                ]
            }
        ]
        
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        result = should_use_dlx_cabin(passengers, all_fares)
        assert result is False

    def test_should_use_dlx_cabin_wrong_aux_type(self):
        """测试辅营类型不是行李时不应该使用DLX舱位"""
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'meal',  # 不是行李
                        'weight': 20.0,
                        'count': 1
                    }
                ]
            }
        ]
        
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        result = should_use_dlx_cabin(passengers, all_fares)
        assert result is False

    def test_should_use_dlx_cabin_empty_passengers_list(self):
        """测试空乘客列表时不应该使用DLX舱位"""
        passengers = []
        
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        result = should_use_dlx_cabin(passengers, all_fares)
        assert result is False

    def test_should_use_dlx_cabin_multiple_baggages_with_20kg(self):
        """测试乘客有多个辅营，其中包含20kg行李时应该使用DLX舱位"""
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'meal',
                        'weight': 0.0,
                        'count': 1
                    },
                    {
                        'aux_type': 'baggage',
                        'weight': 20.0,  # 有20kg行李
                        'count': 1
                    },
                    {
                        'aux_type': 'seat',
                        'weight': 0.0,
                        'count': 1
                    }
                ]
            }
        ]
        
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        result = should_use_dlx_cabin(passengers, all_fares)
        assert result is True

    def test_should_use_dlx_cabin_multiple_baggages_without_20kg(self):
        """测试乘客有多个行李但都不是20kg时不应该使用DLX舱位"""
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 15.0,  # 不是20kg
                        'count': 1
                    },
                    {
                        'aux_type': 'baggage',
                        'weight': 25.0,  # 不是20kg
                        'count': 1
                    }
                ]
            }
        ]
        
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        result = should_use_dlx_cabin(passengers, all_fares)
        assert result is False

    def test_should_use_dlx_cabin_passenger_without_baggages_key(self):
        """测试乘客没有baggages字段时不应该使用DLX舱位"""
        passengers = [
            {
                'passenger_type': 'adult'
                # 没有baggages字段
            }
        ]
        
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        result = should_use_dlx_cabin(passengers, all_fares)
        assert result is False

    def test_should_use_dlx_cabin_baggage_without_aux_type(self):
        """测试行李没有aux_type字段时不应该使用DLX舱位"""
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        # 没有aux_type字段
                        'weight': 20.0,
                        'count': 1
                    }
                ]
            }
        ]
        
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        result = should_use_dlx_cabin(passengers, all_fares)
        assert result is False

    def test_should_use_dlx_cabin_baggage_without_weight(self):
        """测试行李没有weight字段时不应该使用DLX舱位"""
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        # 没有weight字段
                        'count': 1
                    }
                ]
            }
        ]
        
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        result = should_use_dlx_cabin(passengers, all_fares)
        assert result is False

    def test_should_use_dlx_cabin_complex_scenario(self):
        """测试复杂场景：多个乘客，每个乘客有多个辅营"""
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'meal',
                        'weight': 0.0,
                        'count': 1
                    },
                    {
                        'aux_type': 'baggage',
                        'weight': 20.0,  # 有20kg行李
                        'count': 1
                    }
                ]
            },
            {
                'passenger_type': 'child',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20.0,  # 有20kg行李
                        'count': 1
                    },
                    {
                        'aux_type': 'seat',
                        'weight': 0.0,
                        'count': 1
                    }
                ]
            },
            {
                'passenger_type': 'infant',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20.0,  # 有20kg行李
                        'count': 1
                    }
                ]
            }
        ]
        
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        result = should_use_dlx_cabin(passengers, all_fares)
        assert result is True

    def test_should_use_dlx_cabin_edge_case_weight_float(self):
        """测试边界情况：重量为浮点数20.0"""
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20.0,  # 浮点数
                        'count': 1
                    }
                ]
            }
        ]
        
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        result = should_use_dlx_cabin(passengers, all_fares)
        assert result is True

    def test_should_use_dlx_cabin_edge_case_weight_int(self):
        """测试边界情况：重量为整数20"""
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,  # 整数
                        'count': 1
                    }
                ]
            }
        ]
        
        # 添加all_fares参数，包含20kg舱位
        all_fares = {
            "0kg": {"adult": {"quantity": 10}},
            "20kg": {"adult": {"quantity": 5}}
        }
        
        result = should_use_dlx_cabin(passengers, all_fares)
        assert result is True
