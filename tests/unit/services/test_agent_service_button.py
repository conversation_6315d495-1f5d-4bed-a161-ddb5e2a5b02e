#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试agent_service中button参数相关功能的单元测试
符合单元测试规范：在test方法内引入依赖，使用标准mock+断言方式
"""

import pytest
from unittest.mock import Mock, patch, MagicMock


class TestAgentServiceButton:
    """测试AgentService中button参数相关功能"""

    def test_book_step3_switch_to_20kg_button_usage(self):
        """测试_book_step3_switch_to_20kg方法中button参数的使用"""
        # 在test方法内引入依赖避免循环引用
        from app.services.agent_service import AgentService

        # 创建AgentService实例
        service = AgentService(username='test', password='test', airline_code='VJ')

        # Mock client
        service.client = Mock()
        service.client.travel_options = Mock()
        service.client.details = Mock()
        service.set_proxy = Mock()

        # 模拟数据
        new_fare_key = '8,DLX_FARE_KEY'
        passengers = [{'passenger_type': 'adult', 'first_name': 'John', 'last_name': 'Doe'}]

        # Mock返回值
        service.client.travel_options.return_value = 'mocked_travel_options_response'
        service.client.details.return_value = '预订页面 - 座位安置'

        # 调用_book_step3_switch_to_target_cabin方法
        result = service._book_step3_switch_to_target_cabin(new_fare_key, passengers)

        # 验证travel_options被调用了2次
        assert service.client.travel_options.call_count == 2

        # 验证第一次调用是后退操作（button='back'）
        first_call = service.client.travel_options.call_args_list[0]
        args, kwargs = first_call
        assert 'fare_key' in kwargs and kwargs['fare_key'] == new_fare_key
        assert 'button' in kwargs and kwargs['button'] == 'back'

        # 验证第二次调用是重新选择（默认continue）
        second_call = service.client.travel_options.call_args_list[1]
        args, kwargs = second_call
        assert 'fare_key' in kwargs and kwargs['fare_key'] == new_fare_key
        # 第二次调用应该使用默认的continue（不传button参数）
        assert 'button' not in kwargs

    def test_book_step3_switch_error_handling(self):
        """测试_book_step3_switch_to_20kg方法的错误处理"""
        from app.services.agent_service import AgentService

        service = AgentService(username='test', password='test', airline_code='VJ')
        service.client = Mock()
        service.client.travel_options = Mock()
        service.client.details = Mock()
        service.set_proxy = Mock()

        # 模拟数据
        new_fare_key = '8,DLX_FARE_KEY'
        passengers = [{'passenger_type': 'adult', 'first_name': 'John', 'last_name': 'Doe'}]

        # Mock返回值 - details返回错误
        service.client.travel_options.return_value = 'mocked_travel_options_response'
        service.client.details.return_value = '错误页面'

        # Mock agent_helper.get_error_msg
        with patch('app.services.agent_helper.get_error_msg') as mock_get_error_msg:
            mock_get_error_msg.return_value = '测试错误信息'

            # 验证抛出异常
            with pytest.raises(Exception) as exc_info:
                service._book_step3_switch_to_target_cabin(new_fare_key, passengers)

            assert '重新乘客提交失败' in str(exc_info.value)

    def test_book_step2_check_dlx_switch_price_comparison(self):
        """测试_book_step2_check_dlx_switch方法的价格对比逻辑"""
        from app.services.agent_service import AgentService, should_use_dlx_cabin

        service = AgentService(username='test', password='test', airline_code='VJ')

        # 模拟数据
        details_2 = 'mocked_html_content'
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            }
        ]

        all_fares = {
            "0kg": {"adult": {"base": 100.0, "tax": 20.0}, "fare_key": "10,ECO_FARE_KEY"},
            "20kg": {
                "adult": {"base": 120.0, "tax": 30.0},  # 150.0总价，低于ECO+行李175.3
                "fare_key": "8,DLX_FARE_KEY",
            },
        }

        # Mock相关方法
        with patch('app.services.agent_service.should_use_dlx_cabin') as mock_should_use_dlx:
            with patch('app.services.agent_helper.update_baggage_options') as mock_update_baggage:
                with patch('copy.deepcopy') as mock_deepcopy:

                    mock_should_use_dlx.return_value = True
                    mock_deepcopy.return_value = passengers
                    mock_update_baggage.return_value = passengers

                    # 测试应该切换的情况（20kg价格更低）
                    should_switch, new_fare_key = service._book_step2_check_dlx_switch(
                        details_2, passengers, False, all_fares
                    )

                    # 验证结果
                    assert should_switch == True
                    assert new_fare_key == "8,DLX_FARE_KEY"

    def test_book_step2_check_dlx_switch_no_switch(self):
        """测试_book_step2_check_dlx_switch方法不切换的情况"""
        from app.services.agent_service import AgentService

        service = AgentService(username='test', password='test', airline_code='VJ')

        # 模拟数据
        details_2 = 'mocked_html_content'
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_value': '1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1',
                    }
                ],
            }
        ]

        all_fares = {
            "0kg": {"adult": {"base": 100.0, "tax": 20.0}, "fare_key": "10,ECO_FARE_KEY"},
            "20kg": {
                "adult": {"base": 150.0, "tax": 30.0},  # 180.0总价，高于ECO+行李175.3
                "fare_key": "8,DLX_FARE_KEY",
            },
        }

        # Mock相关方法
        with patch('app.services.agent_service.should_use_dlx_cabin') as mock_should_use_dlx:
            with patch('app.services.agent_helper.update_baggage_options') as mock_update_baggage:
                with patch('copy.deepcopy') as mock_deepcopy:

                    mock_should_use_dlx.return_value = True
                    mock_deepcopy.return_value = passengers
                    mock_update_baggage.return_value = passengers

                    # 测试不应该切换的情况（20kg价格更高）
                    should_switch, new_fare_key = service._book_step2_check_dlx_switch(
                        details_2, passengers, False, all_fares
                    )

                    # 验证结果
                    assert should_switch == False
                    assert new_fare_key is None

    def test_book_method_integration(self):
        """测试book方法的完整集成流程"""
        from app.services.agent_service import AgentService

        service = AgentService(username='test', password='test', airline_code='VJ')

        # Mock所有步骤方法
        service._book_step1_add_passengers = Mock()
        service._book_step2_check_dlx_switch = Mock()
        service._book_step3_switch_to_target_cabin = Mock()
        service._book_step4_complete_booking = Mock()

        # 模拟数据
        fare_key = '10,ECO_FARE_KEY'
        passengers = [{'passenger_type': 'adult', 'first_name': 'John', 'last_name': 'Doe', 'birthday': '1990-01-01'}]
        all_fares = {"0kg": {}, "20kg": {}}

        # Mock返回值
        details_2 = 'mocked_details_2'
        service._book_step1_add_passengers.return_value = details_2
        service._book_step2_check_dlx_switch.return_value = (True, '8,DLX_FARE_KEY')
        service._book_step3_switch_to_target_cabin.return_value = 'new_details_2'
        service._book_step4_complete_booking.return_value = {'result': 'success'}

        # 调用book方法
        result = service.book(fare_key=fare_key, passengers=passengers, all_fares=all_fares)

        # 验证各步骤被正确调用
        service._book_step1_add_passengers.assert_called_once_with(fare_key, passengers)
        service._book_step2_check_dlx_switch.assert_called_once_with(details_2, passengers, False, all_fares)
        service._book_step3_switch_to_target_cabin.assert_called_once_with('8,DLX_FARE_KEY', passengers)
        service._book_step4_complete_booking.assert_called_once_with('new_details_2', passengers, False, True)

        # 验证返回结果
        assert result == {'result': 'success'}

    def test_book_method_no_switch(self):
        """测试book方法不切换的情况"""
        from app.services.agent_service import AgentService

        service = AgentService(username='test', password='test', airline_code='VJ')

        # Mock所有步骤方法
        service._book_step1_add_passengers = Mock()
        service._book_step2_check_dlx_switch = Mock()
        service._book_step3_switch_to_target_cabin = Mock()
        service._book_step4_complete_booking = Mock()

        # 模拟数据
        fare_key = '10,ECO_FARE_KEY'
        passengers = [{'passenger_type': 'adult', 'first_name': 'John', 'last_name': 'Doe'}]
        all_fares = {"0kg": {}, "20kg": {}}

        # Mock返回值 - 不需要切换
        details_2 = 'mocked_details_2'
        service._book_step1_add_passengers.return_value = details_2
        service._book_step2_check_dlx_switch.return_value = (False, None)
        service._book_step4_complete_booking.return_value = {'result': 'success'}

        # 调用book方法
        result = service.book(fare_key=fare_key, passengers=passengers, all_fares=all_fares)

        # 验证步骤3没有被调用
        service._book_step3_switch_to_target_cabin.assert_not_called()

        # 验证步骤4使用原始的details_2和use_dlx_cabin=False
        service._book_step4_complete_booking.assert_called_once_with(details_2, passengers, False, False)
