#!/usr/bin/env python3
"""
Agent Helper 单元测试
"""

import pytest
from unittest.mock import patch, MagicMock
from bs4 import BeautifulSoup
from datetime import datetime

from app.services import agent_helper


class TestAgentHelper:
    """Agent Helper 测试类"""

    def test_get_tab_id_success(self):
        """测试获取tab_id - 成功场景"""
        mock_html = """
        <div class="tabsContainer">
            <ul class="tabs_ribbon">
                <li><a id="gridTravelOptDepTab03/06/2025" href="#gridTravelOptDepPane03/06/2025">03/06/2025</a></li>
            </ul>
        </div>
        """

        root_dom = BeautifulSoup(mock_html, 'html.parser')
        result = agent_helper.get_tab_id(root_dom, '03/06/2025')

        # get_tab_id函数返回的是去掉前缀的日期部分
        assert result == '03/06/2025'

    def test_get_tab_id_not_found(self):
        """测试获取tab_id - 未找到"""
        mock_html = """
        <div class="tabsContainer">
            <ul class="tabs_ribbon">
                <li><a id="gridTravelOptDepTab04/06/2025" href="#gridTravelOptDepPane04/06/2025">04/06/2025</a></li>
            </ul>
        </div>
        """

        root_dom = BeautifulSoup(mock_html, 'html.parser')
        result = agent_helper.get_tab_id(root_dom, '03/06/2025')

        assert result is None

    def test_get_flight_info_success(self):
        """测试获取航班信息 - 成功场景"""
        mock_html = """
        <tr>
            <td>03/06/2025</td>
            <td>10:00 BKK</td>
            <td>12:00 SGN</td>
            <td><span class="airlineVJ">VJ123</span></td>
        </tr>
        """

        tr_dom = BeautifulSoup(mock_html, 'html.parser').find('tr')
        result = agent_helper.get_flight_info(tr_dom, airline_code='VJ')

        assert result is not None
        assert result['flight_no'] == 'VJ123'
        assert result['dep_airport_code'] == 'BKK'
        assert result['arr_airport_code'] == 'SGN'
        assert result['dep_time'] == '10:00'
        assert result['arr_time'] == '12:00'
        assert result['dep_date'] == '2025-06-03'
        assert result['arr_date'] == '2025-06-03'

    def test_get_fare_info_success(self):
        """测试获取票价信息 - 成功场景"""
        mock_html = """
        <tr>
            <td data-familyid="Eco">
                <input id="fare" value="100.00" />
                <input id="fare_taxes" value="20.00" />
                <input id="charge_taxes" value="10.00" />
                <input id="charges" value="5.00" />
                <input id="total_complete_charges" value="135.00" />
                <input name="gridTravelOptDep" value="10,ECO_FARE_KEY" />
            </td>
        </tr>
        """

        tr_dom = BeautifulSoup(mock_html, 'html.parser').find('tr')
        result = agent_helper.get_fare_info(tr_dom, passenger_num=1, cabin_level="Eco")

        assert result is not None
        assert result['cabin']['cabin_class'] == 'Eco'
        assert result['cabin']['code'] == 'ECO'
        assert result['cabin']['name'] == '经济舱'
        assert result['adult']['base'] == 100.0
        assert result['adult']['tax'] == 35.0  # (20+10+5)/1
        assert result['adult']['total'] == 135.0
        assert result['adult']['quantity'] == 10
        assert result['fare_key'] == '10,ECO_FARE_KEY'

    def test_get_fare_info_cabin_not_found(self):
        """测试获取票价信息 - 舱位未找到"""
        mock_html = """
        <tr>
            <td data-familyid="Eco">
                <input id="fare" value="100.00" />
            </td>
        </tr>
        """

        tr_dom = BeautifulSoup(mock_html, 'html.parser').find('tr')
        result = agent_helper.get_fare_info(tr_dom, passenger_num=1, cabin_level="SkyBoss")

        assert result is None

    def test_get_fare_info_sold_out(self):
        """测试获取票价信息 - 舱位售完"""
        mock_html = """
        <tr>
            <td data-familyid="Eco">售完</td>
        </tr>
        """

        tr_dom = BeautifulSoup(mock_html, 'html.parser').find('tr')
        result = agent_helper.get_fare_info(tr_dom, passenger_num=1, cabin_level="Eco")

        assert result is None

    def test_extract_weight_success(self):
        """测试提取重量 - 成功场景"""
        text = "手提行李 7 公斤"
        result = agent_helper.extract_weight(text)
        assert result == 7

    def test_extract_weight_not_found(self):
        """测试提取重量 - 未找到"""
        text = "手提行李"
        result = agent_helper.extract_weight(text)
        assert result is None

    def test_get_baggage_info_success(self):
        """测试获取行李信息 - 成功场景"""
        mock_html = """
        <div class="fareRulesMOver" id="ecofarerules">
            <div class="fareRulesContent">
                <li class="greencheckmark">手提行李 7 公斤</li>
                <li class="greencheckmark">托运行李 20 公斤</li>
            </div>
        </div>
        """

        root_dom = BeautifulSoup(mock_html, 'html.parser')
        result = agent_helper.get_baggage_info(root_dom, cabin_level="Eco")

        assert result is not None
        assert 'baggage' in result
        assert 'cabin_baggage' in result['baggage']
        assert 'checked_baggage' in result['baggage']

        # 验证手提行李
        cabin_baggage = result['baggage']['cabin_baggage'][0]
        assert cabin_baggage['weight'] == 7
        assert cabin_baggage['count'] == 1
        assert cabin_baggage['all_weight'] == 7

        # 验证托运行李
        checked_baggage = result['baggage']['checked_baggage'][0]
        assert checked_baggage['weight'] == 20
        assert checked_baggage['count'] == 1
        assert checked_baggage['all_weight'] == 20

    def test_get_baggage_info_not_found(self):
        """测试获取行李信息 - 未找到舱位"""
        mock_html = """
        <div class="fareRulesMOver" id="deluxefarerules">
            <div class="fareRulesContent">
                <li class="greencheckmark">手提行李 7 公斤</li>
            </div>
        </div>
        """

        root_dom = BeautifulSoup(mock_html, 'html.parser')
        result = agent_helper.get_baggage_info(root_dom, cabin_level="Eco")

        assert result == {"baggage": {}}

    def test_get_error_msg_success(self):
        """测试获取错误信息 - 成功场景"""
        html = "<p class='ErrorMessage'>您没有操作此功能的权限</p>"
        result = agent_helper.get_error_msg(html)
        assert result == "您没有操作此功能的权限"

    def test_get_error_msg_not_found(self):
        """测试获取错误信息 - 未找到错误"""
        html = "<p>正常内容</p>"
        result = agent_helper.get_error_msg(html)
        assert result == ""

    @patch('app.services.agent_helper.get_tab_id')
    @patch('app.services.agent_helper.get_baggage_info')
    def test_parse_flight_integration(self, mock_get_baggage_info, mock_get_tab_id):
        """测试parse_flight集成功能"""
        # Mock返回值 - get_tab_id返回去掉前缀的日期
        mock_get_tab_id.return_value = '03/06/2025'
        mock_get_baggage_info.return_value = {
            'baggage': {'cabin_baggage': [{'weight': 7, 'count': 1, 'all_weight': 7}]}
        }

        # 模拟完整的HTML
        mock_html = """
        <div class="tabsContainer">
            <ul class="tabs_ribbon">
                <li><a id="gridTravelOptDepTab03/06/2025" href="#gridTravelOptDepPane03/06/2025">03/06/2025</a></li>
            </ul>
        </div>
        <div id="gridTravelOptDepPane03/06/2025" class="pane">
            <table class="FlightsGrid">
                <tr id="gridTravelOptDep1">
                    <tr>
                        <td>03/06/2025</td>
                        <td>10:00 BKK</td>
                        <td>12:00 SGN</td>
                        <td><span class="airlineVJ">VJ123</span></td>
                    </tr>
                    <tr>
                        <td data-familyid="Eco">
                            <input id="fare" value="100.00" />
                            <input id="fare_taxes" value="20.00" />
                            <input id="charge_taxes" value="10.00" />
                            <input id="charges" value="5.00" />
                            <input id="total_complete_charges" value="135.00" />
                            <input name="gridTravelOptDep" value="10,ECO_FARE_KEY" />
                        </td>
                    </tr>
                </tr>
            </table>
        </div>
        """

        result = agent_helper.parse_flight(
            html=mock_html, date='03/06/2025', passenger_num=1, airline_code='VJ', cabin_level='Eco'
        )

        # 验证结果结构
        assert result is not None
        assert 'results' in result
        assert 'exchange' in result
        assert len(result['results']) == 1

        flight = result['results'][0]
        assert flight['trip_type'] == 'ow'
        assert len(flight['trips']) == 1

        trip = flight['trips'][0]
        assert 'fares' in trip
        assert 'all_fares' in trip
        assert 'includes' in trip

        # 验证mock调用
        mock_get_tab_id.assert_called_once()
        mock_get_baggage_info.assert_called_once()

    def test_extract_free_checked_baggage_weight_chinese_with_or(self):
        """测试中文描述包含'或'字时取第一个公斤数"""
        # 模拟HTML，中文描述包含"或"字
        mock_html = """
        <div class="fareRulesMOver" id="deluxefarerules">
            <div class="fareRulesContent">
                <li class="greencheckmark">免费20公斤或25公斤的托运行李</li>
            </div>
        </div>
        """
        root_dom = BeautifulSoup(mock_html, 'html.parser')

        result = agent_helper.extract_free_checked_baggage_weight(root_dom, "Deluxe")
        assert result == 20  # 应该取第一个公斤数

    def test_extract_free_checked_baggage_weight_english_priority(self):
        """测试英文优先策略"""
        # 模拟HTML，同时有英文和中文描述
        mock_html = """
        <div class="fareRulesMOver" id="skybossfarerules">
            <div class="fareRulesContent">
                <li class="greencheckmark">30kgs checked baggage</li>
                <li class="greencheckmark">免费25公斤的托运行李</li>
            </div>
        </div>
        """
        root_dom = BeautifulSoup(mock_html, 'html.parser')

        result = agent_helper.extract_free_checked_baggage_weight(root_dom, "SkyBoss")
        assert result == 30  # 应该优先取英文的30kg，而不是中文的25kg
