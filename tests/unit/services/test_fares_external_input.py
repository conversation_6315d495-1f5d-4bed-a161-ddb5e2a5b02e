"""
测试fares节点从外部传入的功能
"""

import pytest
from unittest.mock import Mock, patch
from app.services.agent_service import AgentService


class TestFaresExternalInput:
    """测试fares节点从外部传入的功能"""

    def test_fares_from_external_input(self):
        """测试fares节点从外部传入，而不是内部循环查找"""
        service = AgentService(username='test', password='test', airline_code='VJ')

        # 模拟外部传入的fares数据（当前选择的舱位）
        external_fares = {
            'adult': {'base': 53382.66, 'tax': 35105.71, 'total': 176976.74, 'quantity': 79},
            'child': {'base': 53382.66, 'tax': 35105.71, 'total': 176976.74, 'quantity': 79},
            'infant': {'base': 53382.66, 'tax': 35105.71, 'total': 176976.74, 'quantity': 0},
            'cabin': {'cabin_class': 'Eco', 'code': 'W1', 'name': '经济舱', 'desc': '经济舱'}
        }

        # 模拟all_fares数据
        all_fares = {
            '0kg': external_fares,  # 当前选择的舱位
            '20kg': {
                'adult': {'base': 82452.43, 'tax': 37431.29, 'total': 239767.44, 'quantity': 79},
                'child': {'base': 82452.43, 'tax': 37431.29, 'total': 239767.44, 'quantity': 79},
                'infant': {'base': 82452.43, 'tax': 37431.29, 'total': 239767.44, 'quantity': 0},
                'fare_key': '79,W1_DLX,O',
                'cabin': {'cabin_class': 'Deluxe', 'code': 'W1', 'name': '豪华经济舱', 'desc': '豪华经济舱'},
                'free_checked_baggage_kg': 20
            }
        }

        # 模拟乘客数据（有20kg行李）
        passengers = [{
            'passenger_type': 'adult',
            'first_name': 'John',
            'last_name': 'Doe',
            'birthday': '1990-01-01',
            'baggages': [{
                'aux_type': 'baggage',
                'weight': 20.0,
                'from_select_value': '1|NA|3|68189641|20|False|3|Bag 20kgs||NA||0|Baggage 20kgs|100 VND|100|4.42|0|0|1'
            }]
        }]

        with patch('app.services.agent_helper.update_baggage_options') as mock_update_baggage, \
             patch('app.services.agent_helper.extract_baggage_price_from_select_value') as mock_extract_price, \
             patch('app.services.agent_service.should_use_dlx_cabin') as mock_should_use_dlx, \
             patch('copy.deepcopy') as mock_deepcopy:

            # 设置mock返回值
            mock_deepcopy.return_value = passengers
            mock_update_baggage.return_value = passengers
            mock_extract_price.return_value = 100.0  # 行李价格
            mock_should_use_dlx.return_value = True

            # 调用_book_step2_check_dlx_switch方法
            should_switch, new_fare_key = service._book_step2_check_dlx_switch(
                details_2='mock_details',
                passengers=passengers,
                use_dlx_cabin=False,
                all_fares=all_fares,
                fares=external_fares  # 外部传入的fares
            )

            # 验证结果
            assert should_switch == True
            assert new_fare_key == '79,W1_DLX,O'

            # 验证没有调用内部循环查找逻辑
            # 因为fares是直接从外部传入的，不需要内部查找

    def test_price_comparison_uses_external_fares(self):
        """测试价格对比使用外部传入的fares而不是0kg"""
        service = AgentService(username='test', password='test', airline_code='VJ')

        # 模拟外部传入的fares数据（实际选择的舱位价格）
        external_fares = {
            'adult': {'base': 100.0, 'tax': 50.0, 'total': 150.0, 'quantity': 10},
            'child': {'base': 100.0, 'tax': 50.0, 'total': 150.0, 'quantity': 10},
            'infant': {'base': 100.0, 'tax': 50.0, 'total': 150.0, 'quantity': 0},
            'cabin': {'cabin_class': 'Eco', 'code': 'Y', 'name': '经济舱', 'desc': '经济舱'}
        }

        # 模拟all_fares数据（0kg价格不同于外部传入的fares）
        all_fares = {
            '0kg': {
                'adult': {'base': 80.0, 'tax': 40.0, 'total': 120.0, 'quantity': 10},  # 不同的价格
                'child': {'base': 80.0, 'tax': 40.0, 'total': 120.0, 'quantity': 10},
                'infant': {'base': 80.0, 'tax': 40.0, 'total': 120.0, 'quantity': 0},
                'fare_key': '10,ECO_FARE_KEY',
                'cabin': {'cabin_class': 'Eco', 'code': 'Y', 'name': '经济舱', 'desc': '经济舱'},
                'free_checked_baggage_kg': 0
            },
            '20kg': {
                'adult': {'base': 120.0, 'tax': 60.0, 'total': 180.0, 'quantity': 8},
                'child': {'base': 120.0, 'tax': 60.0, 'total': 180.0, 'quantity': 8},
                'infant': {'base': 120.0, 'tax': 60.0, 'total': 180.0, 'quantity': 0},
                'fare_key': '8,DLX_FARE_KEY',
                'cabin': {'cabin_class': 'Deluxe', 'code': 'W', 'name': '豪华经济舱', 'desc': '豪华经济舱'},
                'free_checked_baggage_kg': 20
            }
        }

        # 模拟乘客数据（有20kg行李）
        passengers = [{
            'passenger_type': 'adult',
            'first_name': 'John',
            'last_name': 'Doe',
            'birthday': '1990-01-01',
            'baggages': [{
                'aux_type': 'baggage',
                'weight': 20.0,
                'from_select_value': '1|NA|3|68189641|20|False|3|Bag 20kgs||NA||0|Baggage 20kgs|50 VND|50|4.42|0|0|1'
            }]
        }]

        with patch('app.services.agent_helper.update_baggage_options') as mock_update_baggage, \
             patch('app.services.agent_helper.extract_baggage_price_from_select_value') as mock_extract_price, \
             patch('app.services.agent_service.should_use_dlx_cabin') as mock_should_use_dlx, \
             patch('copy.deepcopy') as mock_deepcopy:

            # 设置mock返回值
            mock_deepcopy.return_value = passengers
            mock_update_baggage.return_value = passengers
            mock_extract_price.return_value = 50.0  # 行李价格
            mock_should_use_dlx.return_value = True

            # 调用_book_step2_check_dlx_switch方法
            should_switch, new_fare_key = service._book_step2_check_dlx_switch(
                details_2='mock_details',
                passengers=passengers,
                use_dlx_cabin=False,
                all_fares=all_fares,
                fares=external_fares  # 外部传入的fares
            )

            # 验证结果：应该不切换，因为外部fares(150)+行李(50)=200 > 20kg舱位(180)
            # 但是如果使用0kg价格(120)+行李(50)=170 < 20kg舱位(180)，结果会不同
            # 这证明了使用外部传入的fares而不是0kg价格
            assert should_switch == False
            assert new_fare_key is None

    def test_book_method_accepts_fares_parameter(self):
        """测试book方法接受fares参数"""
        service = AgentService(username='test', password='test', airline_code='VJ')

        # Mock所有步骤方法
        service._book_step1_add_passengers = Mock()
        service._book_step2_check_dlx_switch = Mock()
        service._book_step4_complete_booking = Mock()

        # 模拟数据
        fare_key = '79,W1_ECO,O'
        passengers = [{'passenger_type': 'adult', 'first_name': 'John', 'last_name': 'Doe', 'birthday': '1990-01-01'}]
        all_fares = {"0kg": {}, "20kg": {}}
        fares = {'adult': {'base': 53382.66, 'tax': 35105.71, 'total': 176976.74}}

        # Mock返回值
        details_2 = 'mocked_details_2'
        service._book_step1_add_passengers.return_value = details_2
        service._book_step2_check_dlx_switch.return_value = (False, None)
        service._book_step4_complete_booking.return_value = {'result': 'success'}

        # 调用book方法，传入fares参数
        result = service.book(
            fare_key=fare_key,
            passengers=passengers,
            all_fares=all_fares,
            fares=fares  # 外部传入的fares
        )

        # 验证_book_step2_check_dlx_switch被正确调用，包含fares参数
        service._book_step2_check_dlx_switch.assert_called_once_with(
            details_2, passengers, False, all_fares, fares
        )

        # 验证返回结果
        assert result == {'result': 'success'}
