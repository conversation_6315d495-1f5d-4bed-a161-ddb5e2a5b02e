from datetime import datetime, timedelta
import random
import time
from typing import Union
import curl_cffi
from curl_cffi import requests
from loguru import logger
import orjson
import urllib.parse

import pytz

from app.services import agent_helper
from commons.decorators import retry_on_exceptions2

# 屏蔽不兼容的浏览器和移动设备标记
BROWSER_TYPES = [
    # Edge
    "edge99",
    "edge101",
    # Chrome
    "chrome99",
    "chrome100",
    "chrome101",
    "chrome104",
    "chrome107",
    "chrome110",
    "chrome116",
    "chrome119",
    "chrome120",
    "chrome123",
    # "chrome124",
    # "chrome99_android",
    # Safari
    "safari15_3",
    "safari15_5",
    "safari17_0",
    # "safari17_2_ios",
    # alias
    # "chrome",
    # "edge",
    # "safari",
    # "safari_ios",
    # "chrome_android",
]

PRINTABLE_TYPES = ['application/json', 'text/html']


class VZAgentClient:
    def __init__(self):
        self.session = requests.Session()

        self.session.headers.update(
            {
                'Host': 'agents.vietjetair.com',
                # 'Cookie': '_ga=GA1.1.*********.1735010340; mp_858bc9dffd91b52dd1a4d241b1b18bf5_mixpanel=%7B%22distinct_id%22%3A%20%226373dde9-ef6e-4618-9506-6bda82b188f6%22%2C%22%24device_id%22%3A%20%22193f761200665a7-0a2d828eed255d-19525634-1fa400-193f761200665a7%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%2C%22mp_lib%22%3A%20%22Rudderstack%3A%20web%22%2C%22utm_source%22%3A%20%22frontpage%22%2C%22%24initial_referrer%22%3A%20%22https%3A%2F%2Fvzinterline.vietjetair.com%2Fen%2Fsearch%3Forigins%3DBKK%26destinations%3DSDJ%26departureDate%3D2025-01-05%26isOneWay%3Dtrue%26currency%3DTHB%26residency%3D%26utm_source%3Dfrontpage%26adults%3D20%22%2C%22%24initial_referring_domain%22%3A%20%22vzinterline.vietjetair.com%22%2C%22%24user_id%22%3A%20%226373dde9-ef6e-4618-9506-6bda82b188f6%22%2C%22currency%22%3A%20%22USD%22%2C%22isMobile%22%3A%20false%2C%22locale%22%3A%20%22en%22%2C%22originalReferrer%22%3A%20%22https%3A%2F%2Fvzinterline.vietjetair.com%2F%22%2C%22partner%22%3A%20%22vietjetair%22%2C%22residency%22%3A%20%22TH%22%2C%22experiment.2024q3-lock-price-2%22%3A%20%22variantLockPrice%22%2C%22experiment.2024q4-remove-lock-price-notification%22%3A%20%22variantRemoveLockPriceNotification%22%7D; _ga_4C6KWLX3N7=GS1.1.1735022157.1.1.1735022200.0.0.0; datadome=MjM44gvpdMl0L9S1iW4aPtuceyzNE45DGkZYKgHuoF4Q2PqCJ2OhRzedxO7W7a40Z2bgHf9xd39jvp5MUsWnZaqT6KSPuj~OjaziYFxqUNpDUe6aIoH8iNZY7jn3ReVP; _atrk_siteuid=n8iTXljcTNryFxvc; appier_random_unique_id_ViewLanding_VietJetAir=3doIHo18nKe1PUdlnD5F26; _fbp=fb.1.1735554298993.305132230315544924; _tt_enable_cookie=1; _ttp=2lkGP8S6b8171OWSF4B3PUu8zlH.tt.1; appier_random_unique_id_Click_LetsGo=MXdsk-qr_KWNHjouXTJb9X; _hjSessionUser_3486574=eyJpZCI6ImEyMjQxNzc4LWZiOGMtNWIxOS04MGIxLTQ2NWY5ODhkZmNjYSIsImNyZWF0ZWQiOjE3MzU1NTQzMDIxNzgsImV4aXN0aW5nIjp0cnVlfQ==; ASP.NET_SessionId=1jl2h0wm1qgxr3bhogz10osm; __utmc=41894834; _gcl_au=1.1.1111672072.1735554297.1811531848.1736820900.1736820900; appier_utmz=%7B%7D; _ga_1DQMSR0C48=GS1.1.1739254577.46.1.1739254595.0.0.0; _ga_6ZGCC82BTL=GS1.1.1739254830.14.0.1739254830.0.0.314322151; _ga_KML2W7BV5C=GS1.1.1739266526.15.0.1739266526.60.0.0; cto_bundle=J0cExF9jRE5QTkh3bm5nciUyQlVjdDFVRjJLV2c5dU4lMkJWTXFBTXBUS2UlMkJhOXc5VGQ3cjI0R3glMkZ4M2RVaVBOTFZtYkZGdExKZG5mT0lWT1Nodnowc2s4ellLUWJubE1PRkV1MEJUYU5XV0clMkI1aE01RkRkZnVDcWdRejBlN2E2ZXhHNHJhdGI4Q0VSTHFyU0duMFB3MDRIZmU1Rm9vREdmUldLdWZEeVlZJTJCeGhOalkxNEklM0Q; __utma=41894834.*********.1735010340.1739266300.1739269238.8; __utmz=41894834.1739269238.8.5.utmcsr=vietjetair.com|utmccn=(referral)|utmcmd=referral|utmcct=/; _ga_Z47TKGJW5K=GS1.1.1739269278.7.1.1739270807.60.0.0; __utmt=1; __utmb=41894834.13.10.1739269238',
                'sec-ch-ua': '"Chromium";v="116", "Not)A;Brand";v="24", "Google Chrome";v="116"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
                'dnt': '1',
                'upgrade-insecure-requests': '1',
                'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36',
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'sec-fetch-site': 'same-site',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-user': '?1',
                'sec-fetch-dest': 'document',
                'accept-language': 'zh-CN,zh;q=0.9',
            }
        )
        self.impersonate = random.choice(BROWSER_TYPES)
        # 在初始化时就设置浏览器伪装
        self.session.impersonate = self.impersonate
        self._view_state = None
        self._view_state_generator = None
        self._debug_id = ''
        self._lst_company_list = ''
        self._host = 'agents.vietjetair.com'
        self._default_host = 'agents.vietjetair.com'

    @property
    def host(self):
        return self._host

    @host.setter
    def host(self, host):
        if host:
            self._host = host
        else:
            self._host = self._default_host

    @retry_on_exceptions2
    def _request(self, method, url, **kwargs) -> Union[None, dict, str, bytes]:
        result = None
        response_content_type = ''
        has_error = False
        try:
            # 根据 host 是否为默认值来决定是否验证证书
            if self.host == self._default_host:
                self.session.verify = kwargs.pop('verify', True)
                logger.info(f'域名访问 {self.host}')
            else:
                # 如果使用IP地址，设置SNI和禁用证书验证
                self.session.verify = False

                # 从URL中提取域名和端口
                parsed_url = urllib.parse.urlparse(url)
                port = 443 if parsed_url.scheme == 'https' else 80

                # 设置RESOLVE选项，将域名解析到指定IP
                resolve_entry = f"{self._default_host}:{port}:{self.host}"
                self.session.curl.setopt(curl_cffi.CurlOpt.RESOLVE, [resolve_entry])

                # 禁用SSL证书验证
                self.session.curl.setopt(curl_cffi.CurlOpt.SSL_VERIFYPEER, 0)
                self.session.curl.setopt(curl_cffi.CurlOpt.SSL_VERIFYHOST, 0)

                # 尝试设置更多的SSL选项来解决握手问题
                try:
                    # 设置SSL版本
                    self.session.curl.setopt(curl_cffi.CurlOpt.SSLVERSION, curl_cffi.CurlSslVersion.TLSv1_2)
                except:
                    try:
                        self.session.curl.setopt(curl_cffi.CurlOpt.SSLVERSION, curl_cffi.CurlSslVersion.TLSv1_3)
                    except:
                        pass

                # 尝试设置加密套件
                try:
                    self.session.curl.setopt(curl_cffi.CurlOpt.SSL_CIPHER_LIST, "DEFAULT")
                except:
                    pass

                # 设置连接超时
                try:
                    self.session.curl.setopt(curl_cffi.CurlOpt.CONNECTTIMEOUT, 30)
                    self.session.curl.setopt(curl_cffi.CurlOpt.TIMEOUT, 60)
                except:
                    pass

                # 保持URL使用域名，让RESOLVE选项处理IP映射
                # 不要修改URL，让resolve选项工作

                logger.info(f'IP访问 {self.host}，使用resolve: {resolve_entry}，URL: {url}')

            if method.upper() == 'POST':
                self.session.headers['referer'] = url + '?lang=zh&st=sl&sesid='
            response = self.session.request(method, url, **kwargs)
            response_content_type = response.headers.get('content-type', '')
            if 'application/json' in response_content_type:
                result = response.json()
            elif 'text/html' in response_content_type:
                result = response.text
            else:
                result = response.content
            self.session.cookies.update(response.cookies.get_dict())

        except curl_cffi.requests.exceptions.Timeout as e:
            has_error = True
            logger.warning(f'{e} {self.session.proxies}')
            raise
        except Exception as e:
            has_error = True
            logger.exception(f'{e} {self.session.proxies}')
            raise
        finally:
            if [c_type for c_type in PRINTABLE_TYPES if c_type in response_content_type]:
                debug_info = {
                    'url': url,
                    'method': method,
                    'headers': kwargs.get('headers', response.request.headers),
                    'cookies': self.session.cookies.get_dict(),
                    'kwargs': kwargs,
                    'primary_ip': response.primary_ip,
                    'tag_ip': response.primary_ip if self._host == self._default_host else self._host,
                }

                if has_error:
                    debug_info['response'] = result

                for j in self.session.cookies.jar:
                    logger.debug(f'{j.name} {j.value} {j.domain} {j.path} {j.expires} {j.secure}')
                logger.debug(f'{debug_info}')
            # time.sleep(random.randint(5, 10))
        return result

    def switch_request(self, url, form_data: Union[dict, list, str] = None, **kwargs) -> Union[None, dict, str, bytes]:
        """切换请求"""
        params = {'lang': 'zh', 'st': 'sl', 'sesid': ''}

        if form_data:
            resp_html = self._request('POST', url, params=params, data=form_data, allow_redirects=True, **kwargs)
            # resp_html = self._request('POST', url, params=params, data=form_data, **kwargs)
        else:
            resp_html = self._request('GET', url, params=params, **kwargs)

        # 试着获取下个页面的入参
        next_data = agent_helper.get_form_data(resp_html)
        logger.debug(f'__VIEWSTATE: {url} old {self._view_state}  new {next_data.get("__VIEWSTATE")}')
        logger.debug(
            f'__VIEWSTATEGENERATOR: {url} old {self._view_state_generator}  new {next_data.get("__VIEWSTATEGENERATOR")}'
        )
        logger.debug(f'DebugID: {url} old {self._debug_id}  new {next_data.get("DebugID")}')
        logger.debug(f'lstCompanyList: {url} old {self._lst_company_list}  new {next_data.get("lstCompanyList")}')

        if next_data.get('__VIEWSTATE'):
            self._view_state = next_data.get('__VIEWSTATE')
        if next_data.get('__VIEWSTATEGENERATOR'):
            self._view_state_generator = next_data.get('__VIEWSTATEGENERATOR')
        if next_data.get('DebugID'):
            self._debug_id = next_data.get('DebugID')
        if next_data.get('lstCompanyList'):
            self._lst_company_list = next_data.get('lstCompanyList')

        return resp_html

    def index(self, **kwargs):
        """首页"""
        url = 'https://www.vietjetair.com/zh-CN'
        headers = {
            'Host': 'www.vietjetair.com',
            # 'Cookie': '_ga=GA1.1.*********.1735010340; mp_858bc9dffd91b52dd1a4d241b1b18bf5_mixpanel=%7B%22distinct_id%22%3A%20%226373dde9-ef6e-4618-9506-6bda82b188f6%22%2C%22%24device_id%22%3A%20%22193f761200665a7-0a2d828eed255d-19525634-1fa400-193f761200665a7%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%2C%22mp_lib%22%3A%20%22Rudderstack%3A%20web%22%2C%22utm_source%22%3A%20%22frontpage%22%2C%22%24initial_referrer%22%3A%20%22https%3A%2F%2Fvzinterline.vietjetair.com%2Fen%2Fsearch%3Forigins%3DBKK%26destinations%3DSDJ%26departureDate%3D2025-01-05%26isOneWay%3Dtrue%26currency%3DTHB%26residency%3D%26utm_source%3Dfrontpage%26adults%3D20%22%2C%22%24initial_referring_domain%22%3A%20%22vzinterline.vietjetair.com%22%2C%22%24user_id%22%3A%20%226373dde9-ef6e-4618-9506-6bda82b188f6%22%2C%22currency%22%3A%20%22USD%22%2C%22isMobile%22%3A%20false%2C%22locale%22%3A%20%22en%22%2C%22originalReferrer%22%3A%20%22https%3A%2F%2Fvzinterline.vietjetair.com%2F%22%2C%22partner%22%3A%20%22vietjetair%22%2C%22residency%22%3A%20%22TH%22%2C%22experiment.2024q3-lock-price-2%22%3A%20%22variantLockPrice%22%2C%22experiment.2024q4-remove-lock-price-notification%22%3A%20%22variantRemoveLockPriceNotification%22%7D; _ga_4C6KWLX3N7=GS1.1.1735022157.1.1.1735022200.0.0.0; datadome=MjM44gvpdMl0L9S1iW4aPtuceyzNE45DGkZYKgHuoF4Q2PqCJ2OhRzedxO7W7a40Z2bgHf9xd39jvp5MUsWnZaqT6KSPuj~OjaziYFxqUNpDUe6aIoH8iNZY7jn3ReVP; _atrk_siteuid=n8iTXljcTNryFxvc; appier_random_unique_id_ViewLanding_VietJetAir=3doIHo18nKe1PUdlnD5F26; _fbp=fb.1.1735554298993.305132230315544924; _tt_enable_cookie=1; _ttp=2lkGP8S6b8171OWSF4B3PUu8zlH.tt.1; appier_random_unique_id_Click_LetsGo=MXdsk-qr_KWNHjouXTJb9X; _hjSessionUser_3486574=eyJpZCI6ImEyMjQxNzc4LWZiOGMtNWIxOS04MGIxLTQ2NWY5ODhkZmNjYSIsImNyZWF0ZWQiOjE3MzU1NTQzMDIxNzgsImV4aXN0aW5nIjp0cnVlfQ==; ASP.NET_SessionId=1jl2h0wm1qgxr3bhogz10osm; __utmc=41894834; _gcl_au=1.1.1111672072.1735554297.1811531848.1736820900.1736820900; appier_utmz=%7B%7D; _ga_1DQMSR0C48=GS1.1.1739254577.46.1.1739254595.0.0.0; _ga_6ZGCC82BTL=GS1.1.1739254830.14.0.1739254830.0.0.314322151; _ga_KML2W7BV5C=GS1.1.1739266526.15.0.1739266526.60.0.0; cto_bundle=J0cExF9jRE5QTkh3bm5nciUyQlVjdDFVRjJLV2c5dU4lMkJWTXFBTXBUS2UlMkJhOXc5VGQ3cjI0R3glMkZ4M2RVaVBOTFZtYkZGdExKZG5mT0lWT1Nodnowc2s4ellLUWJubE1PRkV1MEJUYU5XV0clMkI1aE01RkRkZnVDcWdRejBlN2E2ZXhHNHJhdGI4Q0VSTHFyU0duMFB3MDRIZmU1Rm9vREdmUldLdWZEeVlZJTJCeGhOalkxNEklM0Q; __utma=41894834.*********.1735010340.1739266300.1739269238.8; __utmz=41894834.1739269238.8.5.utmcsr=vietjetair.com|utmccn=(referral)|utmcmd=referral|utmcct=/; _ga_Z47TKGJW5K=GS1.1.1739269278.7.1.1739270807.60.0.0; __utmt=1; __utmb=41894834.13.10.1739269238',
            'sec-ch-ua': '"Chromium";v="116", "Not)A;Brand";v="24", "Google Chrome";v="116"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'dnt': '1',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'sec-fetch-site': 'same-site',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-user': '?1',
            'sec-fetch-dest': 'document',
            'accept-language': 'zh-CN,zh;q=0.9',
        }
        resp = self.session.get(url, headers=headers, **kwargs)
        self.session.cookies.update(resp.cookies.get_dict())

    def login(self, username=None, password=None, **kwargs):
        """登录页"""
        url = f'https://{self._default_host}/sitelogin.aspx?lang=zh'
        form_data = None
        if username and password:
            form_data = {
                '__VIEWSTATE': self._view_state,
                '__VIEWSTATEGENERATOR': self._view_state_generator,
                'SesID': '',
                'DebugID': self._debug_id,
                'txtAgentID': username,
                'txtAgentPswd': password,
            }
            del self.session.headers['referer']
        else:
            self.session.headers['referer'] = 'https://www.vietjetair.com/'
        return self.switch_request(url, form_data, **kwargs)

    def agent_pwd(self, button: str = 'cancel', old_password: str = '', new_password: str = '', **kwargs):
        url = f'https://{self._default_host}/AgentPwd.aspx'
        form_data = {
            '__VIEWSTATE': self._view_state,
            '__VIEWSTATEGENERATOR': self._view_state_generator,
            'button': button,
            'SesID': '',
            'DebugID': 'C6',
            'txtAgentPswdOld': old_password,
            'txtAgentPswdNew': new_password,
            'txtAgentPswdNew2': new_password,
        }
        return self.switch_request(url, form_data=form_data, **kwargs)

    def agent_options(self, currency_code: str = 'THB', **kwargs):
        """代理选项页"""
        url = f'https://{self._default_host}/AgentOptions.aspx?lang=zh&st=sl&sesid='

        form_data = None
        if currency_code:
            form_data = {
                '__VIEWSTATE': self._view_state,
                '__VIEWSTATEGENERATOR': self._view_state_generator,
                "SesID": "",
                "DebugID": self._debug_id,
                'button': kwargs.pop('button', 'bookflight'),
                'selCurrency': currency_code,
            }
            if form_data.get('button') == 'back':
                form_data.update({'PN': '', 'RPN': '', 'OperatedBy': ''})
                del form_data['selCurrency']

        return self.switch_request(url, form_data=form_data, **kwargs)

    def search(self, **kwargs):
        """搜索页"""
        url = f'https://{self._default_host}/ViewFlights.aspx?lang=zh&st=sl&sesid='
        form_data = None
        if kwargs:
            dep_date = kwargs.pop('dep_date')
            form_data = {
                "__VIEWSTATE": self._view_state,
                "__VIEWSTATEGENERATOR": self._view_state_generator,
                "SesID": "",
                "button": "vfto",
                "dlstDepDate_Day": dep_date.strftime('%d'),
                "dlstDepDate_Month": dep_date.strftime('%Y/%m'),
                "dlstRetDate_Day": dep_date.strftime('%d'),
                "dlstRetDate_Month": dep_date.strftime('%Y/%m'),
                "lstDepDateRange": "0",
                "lstRetDateRange": "0",
                "SesID": "",
                "DebugID": self._debug_id,
                "chkRoundTrip": "",
                "lstOrigAP": kwargs.pop('dep'),
                "lstDestAP": kwargs.pop('arr'),
                "departure1": dep_date.strftime('%d/%m/%Y'),
                "departTime1": "0000",
                "departure2": dep_date.strftime('%d/%m/%Y'),
                "departTime2": "0000",
                "lstLvlService": "1",
                "lstResCurrency": kwargs.pop('currency_code'),
                # "txtNumAdults": kwargs.pop('adult'),
                # "txtNumChildren": kwargs.pop('child'),
                # "txtNumInfants": kwargs.pop('infant'),
                "txtNumADT": kwargs.pop('adult'),
                "txtNumCHD": kwargs.pop('child'),
                "txtNumINF": kwargs.pop('infant'),
                "txtPromoCode": "",
                "lstCompanyList": self._lst_company_list,
                "txtPONumber": "",
            }
        logger.debug(form_data)
        return self.switch_request(url, form_data=form_data, **kwargs)

    def travel_options(self, fare_key: str = None, button: str = 'continue', **kwargs):
        """行程选项页"""
        url = f'https://{self._default_host}/TravelOptions.aspx'
        form_data = None
        if fare_key:
            form_data = [
                ('__VIEWSTATE', self._view_state),
                ('__VIEWSTATEGENERATOR', self._view_state_generator),
                ('button', button),
                ('SesID', ''),
                ('DebugID', self._debug_id),
                ('SesID', ''),
                ('DebugID', self._debug_id),
                ('PN', ''),
                ('RPN', ''),
                ('gridTravelOptDep', fare_key),
                ('OperatedBy', ''),
            ]

        return self.switch_request(url, form_data=form_data, **kwargs)

    def details(self, passengers: list = None, button: str = 'continue', **kwargs):
        url = f'https://{self._default_host}/Details.aspx'
        form_data = None
        if passengers:
            form_data = {
                '__VIEWSTATE': self._view_state,
                '__VIEWSTATEGENERATOR': self._view_state_generator,
                'SesID': '',
                'DebugID': self._debug_id,
                'button': button,
                'txtResContact_Name': self._lst_company_list.split('ƒ')[-1].replace(',', ''),
                'txtResContact_PrefLanguage': '4',
                'txtResContact_EMail': '<EMAIL>',
                'txtResContact_Phone': '+848601064931850',
            }
            form_data.update(agent_helper.get_passengers_form_data(passengers))
        return self.switch_request(url, form_data=form_data, **kwargs)

    def addons(self, form_data: list = None, **kwargs):
        url = f'https://{self._default_host}/AddOns.aspx'
        if form_data:
            form_data.append(('__VIEWSTATE', self._view_state))
            form_data.append(('__VIEWSTATEGENERATOR', self._view_state_generator))
            form_data.append(('DebugID', self._debug_id))
        logger.debug(form_data)
        # data_str = vz_agent_helper.convert_form_data_to_body(form_data)
        return self.switch_request(url, form_data=form_data, **kwargs)

    def payment(self, direct_pay: bool = False, **kwargs):
        year = datetime.now().year
        month = datetime.now().month
        if month < 12:
            month += 1
        else:
            year += 1
            month = 1
        dlst_expiry = (datetime(year=year, month=month, day=1) - timedelta(days=1)).strftime('%Y/%m/%d')
        lst_pmt_type = '5,PL,0,PPPSPR,0,0,0'
        button_text = '3rd'
        if direct_pay:
            lst_pmt_type = '4,AG,0,PPPSPR,0,0,0'
            button_text = 'account'
            logger.warning('本次预定使用：余额直接支付')
        else:
            logger.warning('本次预定使用：Pay later 方式锁单')
        data = [
            # ('__VIEWSTATE', '/wEPDwULLTE4NTIyMDYyNDIPZBYCAgIPZBYEAhAPEGRkFgBkAhEPEGRkFgBkZGWRBTrc9N9L8fctkEyM2xieowCs'),
            ('__VIEWSTATE', self._view_state),
            # ('__VIEWSTATEGENERATOR', '8259F811'),
            ('__VIEWSTATEGENERATOR', self._view_state_generator),
            ('button', button_text),
            ('SesID', ''),
            ('DebugID', 'C3'),
            ('SesID', ''),
            ('DebugID', 'C3'),
            ('SesID', ''),
            ('DebugID', 'C3'),
            ('lstPmtType', lst_pmt_type),
            ('txtCardNo', ''),
            # ('dlstExpiry', '2025/02/28'),
            ('dlstExpiry', dlst_expiry),
            ('txtCVC', ''),
            ('txtCardholder', ''),
            ('txtAddr1', ''),
            ('txtCity', ''),
            ('txtPCode', ''),
            ('lstCtry', '-1'),
            ('lstProv', '-1'),
            ('txtPhone', ''),
            ('dangerous_goods_check', '1'),
        ]
        url = f'https://{self._default_host}/Payments.aspx'
        resp = self.switch_request(url=url, form_data=data, **kwargs)
        # logger.debug(resp)
        return resp

    # def terms(self):
    #     url = f'https://{self._default_host}/Terms.aspx'
    #     return self.switch_request(url)

    def confirm(self, passengers: list = None, button: str = 'continue', **kwargs):
        url = f'https://{self._default_host}/Confirm.aspx'
        form_data = None
        if passengers:
            form_data = {
                '__VIEWSTATE': self._view_state,
                '__VIEWSTATEGENERATOR': self._view_state_generator,
                'SesID': '',
                'DebugID': 'C2',
                'button': button,
                'txtResContact_PrefLanguage': '4',
                'chkIAgree': 'on',
            }
            form_data.update(agent_helper.get_confirm_form_data(passengers))

        return self.switch_request(url, form_data=form_data, **kwargs)

    def processing(self, form_data: dict = None, **kwargs):
        url = f'https://{self._default_host}/Processing.aspx'
        if not form_data:
            form_data = {
                '__VIEWSTATE': self._view_state,
                '__VIEWSTATEGENERATOR': self._view_state_generator,
                'SesID': '',
                'DebugID': 'C2',
            }
        return self.switch_request(url, form_data=form_data, **kwargs)

    def search_res(self, real_pnr: str = None, button: str = None, **kwargs):
        url = f'https://{self._default_host}/SearchRes.aspx'
        form_data = None
        if button == 'search':
            form_data = {
                '__VIEWSTATE': self._view_state,
                '__VIEWSTATEGENERATOR': self._view_state_generator,
                'SesID': '',
                'DebugID': 'C6',
                'button': button,
                'txtSearchResNum': real_pnr,
                'txtSearchPhoneNum': '',
                'txtSearchResLName': '',
                'txtSearchResFName': '',
                'lstSearchResDepDate_Day': '',
                'lstSearchResDepDate_Month': '',
                'lstSearchResDepAp': '-1',
                'lstSearchResBookFrDate_Day': '',
                'lstSearchResBookFrDate_Month': '',
                'lstSearchResBookToDate_Day': '',
                'lstSearchResBookToDate_Month': '',
            }
        elif button == 'continue':
            form_data = {
                '__VIEWSTATE': self._view_state,
                '__VIEWSTATEGENERATOR': self._view_state_generator,
                'SesID': '',
                'DebugID': 'C6',
                'button': button,
                'txtSearchResNum': real_pnr,
                'txtSearchPhoneNum': '',
                'txtSearchResLName': '',
                'txtSearchResFName': '',
                'lstSearchResDepDate_Day': '',
                'lstSearchResDepDate_Month': '',
                'lstSearchResDepAp': '-1',
                'lstSearchResBookFrDate_Day': '',
                'lstSearchResBookFrDate_Month': '',
                'lstSearchResBookToDate_Day': '',
                'lstSearchResBookToDate_Month': '',
                'gridResSearch': real_pnr,
            }

        return self.switch_request(url, form_data=form_data, **kwargs)

    def edit_recind(self, form_data: dict = None, **kwargs):
        url = f'https://{self._default_host}/EditRecindFunction.aspx'
        if form_data:
            form_data.update({'__VIEWSTATE': self._view_state, '__VIEWSTATEGENERATOR': self._view_state_generator})
        return self.switch_request(url, form_data=form_data, **kwargs)

    def edit_res(self, form_data: dict = None, button: str = 'addpayment', **kwargs):
        url = f'https://{self._default_host}/EditRes.aspx'
        if form_data:
            form_data.update(
                {'__VIEWSTATE': self._view_state, '__VIEWSTATEGENERATOR': self._view_state_generator, 'button': button}
            )
        return self.switch_request(url, form_data=form_data, **kwargs)

    def add_payment(self, form_data: dict = None, direct_pay: bool = False, **kwargs):
        year = datetime.now().year
        month = datetime.now().month
        if month < 12:
            month += 1
        else:
            year += 1
            month = 1
        dlst_expiry = (datetime(year=year, month=month, day=1) - timedelta(days=1)).strftime('%Y/%m/%d')
        lst_pmt_type = '5,PL,0,PPPSPR,0,0,0'
        button_text = '3rd'
        if direct_pay:
            lst_pmt_type = '4,AG,0,PPPSPR,0,0,0'
            button_text = 'account'
            logger.warning('本次预定使用：余额直接支付')
        else:
            logger.warning('本次预定使用：Pay later 方式锁单')
        if form_data:
            form_data.update(
                {
                    '__VIEWSTATE': self._view_state,
                    '__VIEWSTATEGENERATOR': self._view_state_generator,
                    'button': button_text,
                    'lstPmtType': lst_pmt_type,
                    'dlstExpiry': dlst_expiry,
                }
            )

        url = f'https://{self._default_host}/AddPayment.aspx'
        resp = self.switch_request(url=url, form_data=form_data, **kwargs)
        # logger.debug(resp)
        return resp
