import asyncio
import copy
from datetime import datetime, timedelta
from math import ceil
from typing import List, Any

import random
import re
import time

import curl_cffi
from loguru import logger
import orjson


from app.clients.agent_client import VZAgentClient


from app.config import settings, new_settings
from app.extensions.redis_locker import SYNC_REDIS_CFG
from app.services import agent_helper, callback_service, proxy_service, vj_search_service

import re
from bs4 import BeautifulSoup

from commons.consts.api_codes import ApiCodes
from commons.extensions.redis_extras import SyncRedisPool
from commons import sdks as hy_sdks
from commons.utils import CipherUtils


class AgentService:
    def __init__(self, username, password, airline_code: str) -> None:
        self.client = VZAgentClient()
        self.username = username
        self.password = password
        self._view_state = None
        self._view_state_generator = None
        # 航空公司代码，改成必传，后续不在维护vz_crawler代码
        # 逐步用VJ项目的VZAgentService替换VZ相关功能
        self.airline_code = airline_code

    def set_proxy(self, proxy_group: str = 'default'):
        proxies, proxy_auth, proxy_str, host = proxy_service.get_proxy(
            proxy_group=proxy_group, airline_code=self.airline_code
        )
        if proxies:
            self.client.session.proxies = proxies
        if proxy_auth:
            self.client.session.proxy_auth = proxy_auth

    def login(self):
        result = None
        err_ext = None
        for i in range(3):
            try:
                login_1 = self.client.login()
                login_2 = self.client.login(username=self.username, password=self.password)

                if '新密码:' in login_2:
                    login_2 = self.client.agent_pwd()

                result = agent_helper.get_form_data(login_2)
                if '预订页面 - 代理方案' in login_2:
                    logger.info(f'登录成功 login_2')
                    break
                else:
                    time.sleep(1)
                    err_ext = agent_helper.get_error_msg(login_2)
                    logger.warning(f'登录失败 login_2 {login_2}')
                    self.set_proxy()
                    logger.warning(f'重新登录 login_2 {i+1} 次')

            except Exception as e:
                logger.exception(e)
            finally:
                pass
        else:
            raise Exception(f'登录失败 {err_ext}')

        return result

    def save_cookies(self, key: str = 'vz_agent_cookies'):
        with SyncRedisPool(**SYNC_REDIS_CFG) as redis:
            cache_data = {
                '__VIEWSTATE': self.client._view_state,
                '__VIEWSTATEGENERATOR': self.client._view_state_generator,
                'lstCompanyList': self.client._lst_company_list,
                'cookies': self.client.session.cookies.get_dict(),
            }
            redis.set(key, orjson.dumps(cache_data).decode('utf-8'), ex=60 * 10)
        return True

    def load_cookies(self, key: str = 'vz_agent_cookies'):
        cookies = {}
        with SyncRedisPool(**SYNC_REDIS_CFG) as redis:
            cache_str = redis.get(key)
            if cache_str:
                cache_data = orjson.loads(cache_str)
                cookies = cache_data.get('cookies', {})
                self.client._view_state = cache_data.get('__VIEWSTATE')
                self.client._view_state_generator = cache_data.get('__VIEWSTATEGENERATOR')
                self.client._lst_company_list = cache_data.get('lstCompanyList')
        self.client.session.cookies.update(cookies)
        return cookies

    def back_index(self):
        self.client.agent_options()
        self.client.agent_options(button='back')

    def search(
        self, dep: str, arr: str, date: str, adult: int = 1, child: int = 0, infant: int = 0, currency_code: str = 'THB'
    ):
        result = None
        try:
            # 1. 跳转查询页面
            if not self.client._view_state:
                self.client.agent_options()
            agent_opt_2 = self.client.agent_options(currency_code=currency_code)
            # next_data = get_form_data(agent_options_2)
            # logger.debug(next_data)

            # 2. 查询
            dep_date = datetime.strptime(date, '%Y-%m-%d')

            search_2 = self.client.search(
                dep=dep,
                arr=arr,
                dep_date=dep_date,
                adult=adult,
                child=child,
                infant=infant,
                currency_code=currency_code,
                timeout=10,
                max_retries=3,
                pre_call=self.set_proxy,
                exceptions=(
                    curl_cffi.requests.exceptions.Timeout,
                    curl_cffi.requests.exceptions.HTTPError,
                    curl_cffi.requests.exceptions.ConnectionError,
                ),
            )
            search_error = agent_helper.get_error_msg(search_2)
            if search_error:
                ApiCodes.BOOK_SEARCH_FAIL.raise_error(ext_msg=search_error)
            logger.debug(search_2)

            result = agent_helper.parse_flight(
                html=search_2,
                date=dep_date.strftime('%d/%m/%Y'),
                passenger_num=adult + child + infant,
                airline_code=self.airline_code,
            )
            # result = vz_agent_helper.get_form_data(search_2)
            result['extra'] = {
                'cookies': self.client.session.cookies.get_dict(),
                'view_state': self.client._view_state,
                'view_state_generator': self.client._view_state_generator,
                'lst_company_list': self.client._lst_company_list,
            }

        except Exception as e:
            logger.exception(e)
        finally:
            pass
        return result

    def book(
        self,
        fare_key: str,
        passengers: list,
        direct_pay: bool = False,
        use_dlx_cabin: bool = False,
        all_fares: dict = None,
    ):
        """
        预订航班
        Args:
            fare_key: 票价key
            passengers: 乘客列表
            direct_pay: 是否直接支付
            use_dlx_cabin: 是否使用DLX舱位
            all_fares: 所有舱位价格信息
        """
        # 第一步：选择航班和添加乘客
        details_2 = self._book_step1_add_passengers(fare_key, passengers)

        # 第二步：判断是否需要后退切换到打包舱位
        should_switch, new_fare_key = self._book_step2_check_dlx_switch(details_2, passengers, use_dlx_cabin, all_fares)

        if should_switch:
            # 第三步：执行后退操作
            logger.info(f'执行后退操作，切换到打包舱位，新fare_key: {new_fare_key}')
            details_2 = self._book_step3_switch_to_target_cabin(new_fare_key, passengers)
            use_dlx_cabin = True  # 切换后使用打包舱位逻辑
        else:
            logger.info('不需要后退切换到打包舱位')
        # todo test
        exit(0)
        # 第四步：继续执行预订流程
        return self._book_step4_complete_booking(details_2, passengers, direct_pay, use_dlx_cabin)

    def _book_step1_add_passengers(self, fare_key: str, passengers: list) -> str:
        """
        第一步：选择航班和添加乘客
        """
        # 选择航班和报价（加购物车）
        travel_opt_2 = self.client.travel_options(
            fare_key=fare_key,
            max_retries=3,
            pre_call=self.set_proxy,
            exceptions=(
                curl_cffi.requests.exceptions.Timeout,
                curl_cffi.requests.exceptions.HTTPError,
                curl_cffi.requests.exceptions.ConnectionError,
            ),
        )

        # 循环判断，如果儿童在第一个，则移到末尾
        for i, passenger in enumerate(passengers):
            if passenger['passenger_type'] == 'child':
                passengers.append(passengers.pop(i))
                break

        # 如果全部乘客都是儿童则抛出异常
        if len([p for p in passengers if p['passenger_type'] == 'child']) == len(passengers):
            raise Exception('全部乘客都是儿童，无法预订')

        # 填写乘机人
        details_2 = self.client.details(
            passengers=passengers,
            max_retries=3,
            pre_call=self.set_proxy,
            exceptions=(curl_cffi.requests.exceptions.HTTPError, curl_cffi.requests.exceptions.ConnectionError),
        )

        if '预订页面 - 座位安置' not in details_2:
            err_ext = agent_helper.get_error_msg(details_2)
            logger.warning(f'乘客提交失败 details {err_ext}')
            raise Exception(f'乘客提交失败 details {err_ext}')
        else:
            logger.info(f'乘客提交成功 details_2')

        return details_2

    def _book_step2_check_dlx_switch(
        self, details_2: str, passengers: list, use_dlx_cabin: bool, all_fares: dict
    ) -> tuple:
        """
        第二步：判断是否需要后退切换到打包舱位
        返回: (是否需要切换, 新的fare_key)
        """
        if use_dlx_cabin or not all_fares or not should_use_dlx_cabin(passengers, all_fares):
            return False, None

        # 先用最低舱位执行到update_baggage_options
        passengers_copy = copy.deepcopy(passengers)
        passengers_copy = agent_helper.update_baggage_options(
            airline_code=self.airline_code, html_content=details_2, passengers=passengers_copy
        )

        # 获取乘客购买的行李重量（所有乘客重量一致）
        baggage_weight = None
        for passenger in passengers:
            for baggage in passenger.get('baggages', []):
                if baggage and baggage.get('aux_type') == 'baggage':
                    baggage_weight = baggage.get('weight')
                    break
            if baggage_weight:
                break

        if not baggage_weight:
            return False, None

        # 构建目标舱位key，转换为整数以匹配all_fares的key格式
        target_kg_key = f"{int(baggage_weight)}kg"

        # 检查目标舱位是否存在
        if target_kg_key not in all_fares:
            return False, None

        # 计算最低舱位+行李的总价格
        lowest_fare_total = 0
        baggage_total = 0

        # 获取最低舱位价格（通常是0kg）
        lowest_kg_key = min(all_fares.keys(), key=lambda x: int(x.replace('kg', '')))
        if lowest_kg_key in all_fares:
            lowest_fare = all_fares[lowest_kg_key]
            lowest_fare_total = lowest_fare["adult"]["base"] + lowest_fare["adult"]["tax"]

        # 计算行李价格
        for passenger in passengers_copy:
            for baggage in passenger.get('baggages', []):
                if baggage and baggage.get('from_select_value'):
                    baggage_price = agent_helper.extract_baggage_price_from_select_value(baggage['from_select_value'])
                    baggage_total += baggage_price

        # 获取目标舱位价格
        target_fare = all_fares[target_kg_key]
        target_fare_total = target_fare["adult"]["base"] + target_fare["adult"]["tax"]

        logger.info(
            f'价格对比: 最低舱位({lowest_kg_key})+行李={lowest_fare_total + baggage_total}, 目标舱位({target_kg_key})={target_fare_total}'
        )

        # 如果目标舱位价格更低，则需要切换
        if target_fare_total < (lowest_fare_total + baggage_total):
            return True, target_fare["fare_key"]

        return False, None

    def _book_step3_switch_to_target_cabin(self, new_fare_key: str, passengers: list) -> str:
        """
        第三步：执行后退操作，切换到目标打包舱位
        """
        # 后退到travel_options页面
        back_travel_opt = self.client.travel_options(
            fare_key=new_fare_key,
            button='back',
            max_retries=3,
            pre_call=self.set_proxy,
            exceptions=(
                curl_cffi.requests.exceptions.Timeout,
                curl_cffi.requests.exceptions.HTTPError,
                curl_cffi.requests.exceptions.ConnectionError,
            ),
        )

        # 重新选择目标舱位
        travel_opt_2 = self.client.travel_options(
            fare_key=new_fare_key,
            max_retries=3,
            pre_call=self.set_proxy,
            exceptions=(
                curl_cffi.requests.exceptions.Timeout,
                curl_cffi.requests.exceptions.HTTPError,
                curl_cffi.requests.exceptions.ConnectionError,
            ),
        )

        # 重新填写乘机人
        details_2 = self.client.details(
            passengers=passengers,
            max_retries=3,
            pre_call=self.set_proxy,
            exceptions=(curl_cffi.requests.exceptions.HTTPError, curl_cffi.requests.exceptions.ConnectionError),
        )

        if '预订页面 - 座位安置' not in details_2:
            err_ext = agent_helper.get_error_msg(details_2)
            logger.warning(f'重新乘客提交失败 details {err_ext}')
            raise Exception(f'重新乘客提交失败 details {err_ext}')
        else:
            logger.info(f'重新乘客提交成功，使用打包舱位')

        return details_2

    def _book_step4_complete_booking(self, details_2: str, passengers: list, direct_pay: bool, use_dlx_cabin: bool):
        """
        第四步：完成预订流程
        """
        addons = agent_helper.get_form_data(details_2, ignore_value_check=True)
        logger.debug(addons)

        # 如果使用打包舱位，跳过行李选择，因为打包舱位已包含行李
        if not use_dlx_cabin:
            passengers = agent_helper.update_baggage_options(
                airline_code=self.airline_code, html_content=details_2, passengers=passengers
            )
        else:
            logger.info('使用打包舱位出票，跳过行李选择')

        addons_form_data = agent_helper.get_addons_form_data(addons=addons, passengers=passengers)
        err_ext = None

        for i in range(1):
            try:
                addons_2 = self.client.addons(
                    form_data=addons_form_data,
                    max_retries=3,
                    pre_call=self.set_proxy,
                    exceptions=(curl_cffi.requests.exceptions.HTTPError, curl_cffi.requests.exceptions.ConnectionError),
                )

                if '预订页面 - 付款资讯' in addons_2:
                    logger.info(f'辅营提交成功 addons_2')
                    break
                else:
                    time.sleep(1)
                    err_ext = agent_helper.get_error_msg(addons_2)
                    logger.warning(f'重新提交辅营 addons_2 {i+1} 次')

            except Exception as e:
                logger.exception(e)
            finally:
                pass
        else:
            raise Exception(f'辅营提交失败 addons {err_ext}')

        book_fare_info = agent_helper.get_book_fare(addons_2)
        payment_2 = self.client.payment(
            max_retries=3,
            pre_call=self.set_proxy,
            exceptions=(curl_cffi.requests.exceptions.HTTPError, curl_cffi.requests.exceptions.ConnectionError),
            direct_pay=direct_pay,
        )
        if '预订页面 - 确认选择' not in payment_2:
            err_ext = agent_helper.get_error_msg(payment_2)
            logger.warning(f'支付提交失败 payment {err_ext}')
            raise Exception(f'支付提交失败 payment {err_ext}')
        else:
            logger.info(f'支付提交成功 payment_2')

        if 'The server was too busy to process your request' in payment_2:
            raise Exception('The server was too busy to process your request')

        confirm_2 = self.client.confirm(
            passengers=passengers,
            max_retries=3,
            pre_call=self.set_proxy,
            exceptions=(curl_cffi.requests.exceptions.HTTPError, curl_cffi.requests.exceptions.ConnectionError),
        )
        if '预订页面 -旅程' not in confirm_2:
            err_ext = agent_helper.get_error_msg(confirm_2)
            logger.warning(f'订单确认失败 confirm {err_ext}')
            raise Exception(f'订单确认失败 confirm {err_ext}')
        else:
            logger.info(f'订单确认成功 confirm_2')

        time.sleep(random.randint(1, 3))
        process_form_data = agent_helper.get_form_data(confirm_2)
        processing_2 = self.client.processing(form_data=process_form_data)
        if '预订页面 -旅程' not in processing_2:
            err_ext = agent_helper.get_error_msg(processing_2)
            logger.warning(f'PNR生成失败 processing {err_ext}')
            raise Exception(f'PNR生成失败 processing {err_ext}')
        else:
            logger.info(f'PNR生成成功 processing_2')
        logger.debug(processing_2)
        result = agent_helper.parse_book_result(html=processing_2, airline_code=self.airline_code)
        result['fare'] = book_fare_info
        logger.info(f'预订结果：{result}')

        return result

    def confirm_pay(
        self,
        order_no: str,
        real_pnr: str,
        currency_code: str,
        total_price: float,
        dep_date: str = None,
        dep_time: str = None,
        dep_diff_minutes: int = None,
    ):
        # 请求前必须先get
        search_res_0 = self.client.search_res()

        search_res_1 = self.client.search_res(real_pnr=real_pnr, button='search')

        if '预订编辑页面 - 搜索预订' not in search_res_1 or real_pnr not in search_res_1:
            err_ext = agent_helper.get_error_msg(search_res_1)
            logger.warning(f'{search_res_1} 订单查询失败 search {err_ext}')
            raise Exception(f'订单查询失败 search {err_ext}')
        else:
            logger.info(f'订单查询成功 search_res_1')

        edit_recind_1 = self.client.search_res(real_pnr=real_pnr, button='continue')
        if '预订编辑页面 -撤销功能要求' not in edit_recind_1:
            err_ext = agent_helper.get_error_msg(edit_recind_1)
            logger.warning(f'{edit_recind_1} 订单选择失败 continue {err_ext}')
            raise Exception(f'订单选择失败 continue {err_ext}')
        else:
            logger.info(f'订单选择成功 search_res_2')

        edit_recind_data = agent_helper.get_form_data(edit_recind_1)
        edit_res_1 = self.client.edit_recind(form_data=edit_recind_data)
        if '预订编辑页面 - 编辑预订' not in edit_res_1:
            err_ext = agent_helper.get_error_msg(edit_res_1)
            logger.warning(f'{edit_res_1} 进入订单编辑失败 edit_recind {err_ext}')
            raise Exception(f'进入订单编辑失败 edit_recind {err_ext}')
        else:
            logger.info(f'进入订单编辑成功 edit_recind_2')

        pay_info = agent_helper.get_paid_amount_from_edit_res(edit_res_1)
        logger.debug(f'pay_info: {pay_info}')
        if pay_info['paid_amount'] > 0:
            ApiCodes.BOOK_PAID_ERROR.raise_error(
                ext_msg=f'订单已支付 {pay_info["paid_amount"]} {pay_info["currency_code"]}'
            )

        if dep_date and dep_time and dep_diff_minutes:
            flight_info = agent_helper.get_flight_info_from_edit_res(edit_res_1)
            if len(dep_time.split(':')) == 2:
                dep_time = f'{dep_time}:00'
            compare_dep_time(
                dep_date=dep_date,
                dep_time=dep_time,
                dep_diff_minutes=dep_diff_minutes,
                flight_time=f'{flight_info["dep_date"]} {flight_info["dep_time"]}',
            )
        else:
            logger.debug(f'跳过出发时间校验 {dep_date} {dep_time} {dep_diff_minutes}')

        edit_res_data = agent_helper.get_form_data(edit_res_1)
        logger.debug(f'edit_res_data: {edit_res_data}')
        add_payment_1 = self.client.edit_res(form_data=edit_res_data)

        if '预订编辑页面 - 添加账户付' not in add_payment_1:
            err_ext = agent_helper.get_error_msg(add_payment_1)
            logger.warning(f'{add_payment_1} 进入付款页面失败 edit_res {err_ext}')
            raise Exception(f'进入付款页面失败 edit_res {err_ext}')
        else:
            logger.info(f'进入付款页面成功 edit_res_2')
        pay_result = agent_helper.get_paid_amount_from_edit_res(edit_res_1)
        # pay_result = agent_helper.get_pay_result(add_payment_1)
        logger.debug(f'pay_result: {pay_result}')

        if pay_result['total_amount'] != total_price:
            err_ext = f'总费用不匹配: 应付 {pay_result["total_amount"]} != 预定金额 {total_price}'
            raise Exception(err_ext)
        else:
            logger.info(f'总费用匹配 pay_result')

        if pay_result['currency_code'] != currency_code:
            err_ext = f'币种不匹配: 应付 {pay_result["currency_code"]} != 预定币种 {currency_code}'
            raise Exception(err_ext)
        else:
            logger.info(f'币种匹配 pay_result')

        add_payment_data = agent_helper.get_form_data(add_payment_1)
        logger.debug(f'add_payment_data: {add_payment_data}')
        add_payment_2 = self.client.add_payment(form_data=add_payment_data, direct_pay=settings.OPEN_DIRECT_PAY)
        if '预订页面 -旅程' not in add_payment_2:
            err_ext = agent_helper.get_error_msg(add_payment_2)
            logger.warning(f'{add_payment_2} 提交支付方式失败 add_payment {err_ext}')
            raise Exception(f'提交支付方式失败 add_payment {err_ext}')
        else:
            logger.info(f'提交支付方式成功 add_payment_2')

        time.sleep(random.randint(1, 3))
        # for i in range(3):
        process_form_data = agent_helper.get_form_data(add_payment_2)
        processing_2 = self.client.processing(form_data=process_form_data)
        if (
            '预订编辑页面 -编辑预订结果' not in processing_2
            or real_pnr not in processing_2
            or '更改好的旅程资讯已寄到预定人的邮件' not in processing_2
        ):
            err_ext = agent_helper.get_error_msg(processing_2)
            logger.warning(f'{processing_2} 提交支付失败 processing {err_ext}')
            raise Exception(f'提交支付失败 processing {err_ext}')
        else:
            logger.info(f'提交支付成功 processing_2')
        logger.debug(f'processing_2: {processing_2}')
        return pay_result

    def get_order_info(self, real_pnr: str):
        # 请求前必须先get
        search_res_0 = self.client.search_res()

        search_res_1 = self.client.search_res(real_pnr=real_pnr, button='search')

        if '预订编辑页面 - 搜索预订' not in search_res_1 or real_pnr not in search_res_1:
            err_ext = agent_helper.get_error_msg(search_res_1)
            logger.warning(f'{search_res_1} 订单查询失败 search {err_ext}')
            raise Exception(f'订单查询失败 search {err_ext}')
        else:
            logger.info(f'订单查询成功 search_res_1')

        edit_recind_1 = self.client.search_res(real_pnr=real_pnr, button='continue')
        if '预订编辑页面 -撤销功能要求' not in edit_recind_1:
            err_ext = agent_helper.get_error_msg(edit_recind_1)
            logger.warning(f'{edit_recind_1} 订单选择失败 continue {err_ext}')
            raise Exception(f'订单选择失败 continue {err_ext}')
        else:
            logger.info(f'订单选择成功 search_res_2')

        edit_recind_data = agent_helper.get_form_data(edit_recind_1)
        edit_res_1 = self.client.edit_recind(form_data=edit_recind_data)
        if '预订编辑页面 - 编辑预订' not in edit_res_1:
            err_ext = agent_helper.get_error_msg(edit_res_1)
            logger.warning(f'{edit_res_1} 进入订单编辑失败 edit_recind {err_ext}')
            raise Exception(f'进入订单编辑失败 edit_recind {err_ext}')
        else:
            logger.info(f'进入订单编辑成功 edit_recind_2')

        pay_info = agent_helper.get_paid_amount_from_edit_res(edit_res_1)
        return pay_info


def get_account(airline_code: str, account_name: str = None):
    fare_sdk = hy_sdks.get_sub_sdk('flight_fare', settings.FLIGHT_FARE_URL)
    result = fare_sdk.get_airline_account(airline_code=airline_code, account_name=account_name)
    if result and result.get('code') == ApiCodes.SUCCESS.value and result.get('data'):
        return {
            'username': result['data']['username'],
            'password': CipherUtils.aes_decrypt(key=settings.AES_KEY, ciphertext=result['data']['password']),
        }
    else:
        raise Exception('航空公司账号不存在')


def get_fixed_account(airline_code: str, dep_airport_code: str, arr_airport_code: str):

    account_name = None
    agent_account_rules = new_settings.get(f'agent_account_rules.{airline_code}')
    if agent_account_rules:
        today = datetime.now().date()
        start_date = datetime.strptime(agent_account_rules['data_range'][0], '%Y-%m-%d').date()
        end_date = datetime.strptime(agent_account_rules['data_range'][1], '%Y-%m-%d').date()
        if today >= start_date and today <= end_date:
            if f'{dep_airport_code}-{arr_airport_code}' in agent_account_rules['lines']:
                account_name = agent_account_rules['account']
                logger.info(f'使用航空公司账号：{account_name}')
            else:
                logger.info('使用默认航空公司账号')
    return account_name


def run_verify_book(params: dict):
    # hosts = ['**************', '**************']
    # for host in hosts:
    from commons.extensions.logger_extras import log_uid, log_server_name

    result = {
        'task_info': copy.deepcopy(params),
        'data': None,
        'error': {'code': ApiCodes.UNKNOWN.value, 'message': ApiCodes.UNKNOWN.label},
    }
    airline_code = params['airline_code']
    today = datetime.now().date()

    account_name = get_fixed_account(
        airline_code=airline_code,
        dep_airport_code=params['dep_airport_code'],
        arr_airport_code=params['arr_airport_code'],
    )

    account = get_account(airline_code=airline_code, account_name=account_name)
    sv = AgentService(username=account['username'], password=account['password'], airline_code=airline_code)
    # sv.client.host = host
    try:

        if not log_server_name.get():
            log_server_name.set(f'{airline_code.lower()}_crawler')
        log_uid.set(params['unique_id'])

        sv.set_proxy()
        sv.client.session.timeout = 120
        sv.login()
        search_rs = sv.search(
            dep=params['dep_airport_code'],
            arr=params['arr_airport_code'],
            date=params['dep_date'],
            adult=params['adult'],
            child=params['child'],
            infant=params['infant'],
            currency_code=params['currency_code'],
        )
        if not search_rs or not search_rs['results']:
            return ApiCodes.BOOK_SEARCH_FAIL.raise_error()

        flight = [f for f in search_rs['results'] if f['trips'][0]['flight_nos'][0] == params['flight_no']]
        if flight:
            flight = flight[0]
        else:
            return ApiCodes.BOOK_FLIGHT_NOT_FOUND.raise_error(ext_msg=params['flight_no'])
        logger.debug(flight)
        src_total = ceil(params['src_adult_base'] + params['src_adult_tax'])
        fare_total = ceil(flight['trips'][0]['fares']['adult']['base'] + flight['trips'][0]['fares']['adult']['tax'])
        if fare_total > src_total:
            ext_msg = f'新总价 {fare_total} 高于原总价 {src_total} 币种：{params["currency_code"]}'
            logger.warning(ext_msg)
            return ApiCodes.BOOK_PRICE_ERROR.raise_error(ext_msg=ext_msg)
        logger.debug(f'src_total: {src_total}, fare_total: {fare_total}')

        logger.debug(flight)
        # data['flight'] = flight
        fare_key = flight['extra']['fare_key']
        ticket_num = int(fare_key.split(',')[0])
        if ticket_num < params['adult'] + params['child'] + params['infant']:
            # return ApiCodes.BOOK_TICKET_NOT_ENOUGH.raise_error()
            logger.warning(
                f'票数不足 {params["adult"] + params["child"] + params["infant"]} < {ticket_num}, fare_key:{fare_key}, 尝试预订'
            )
            # return ApiCodes.BOOK_TICKET_NOT_ENOUGH.raise_error()

        # 传递all_fares信息给book方法，让book方法内部处理DLX切换逻辑
        all_fares = flight['trips'][0].get('all_fares', {})
        book_rs = sv.book(fare_key=fare_key, passengers=params['passengers'], all_fares=all_fares)
        # if 'fare' not in book_rs:
        #     book_rs['fare'] = sv.get_order_info(real_pnr=book_rs['pnr'])
        # if not book_rs.get('fare', {}).get('currency'):
        #     book_rs['fare']['currency'] = params['currency_code']
        # data['book'] = book_rs

        result['data'] = {'flight': flight, 'book': book_rs, 'account_name': account['username']}
        result['error']['code'] = ApiCodes.SUCCESS.value
        result['error']['message'] = ApiCodes.SUCCESS.label
        # break
    except Exception as e:
        if len(e.args) > 1:
            result['error'] = {'code': e.args[0], 'message': e.args[1]}
            logger.warning(e)
        else:
            result['error'] = {'code': ApiCodes.UNKNOWN.value, 'message': str(e)}
            logger.exception(e)
    finally:
        result['task_info']['host'] = sv.client.host
        result['task_info']['proxy'] = list(sv.client.session.proxies.values())[0] if sv.client.session.proxies else ''

    return result


def compare_dep_time(dep_date: str, dep_time: str, dep_diff_minutes: int, flight_time: str):
    if not dep_time or not flight_time:
        logger.info('出发时间或航班时间为空，跳过出发时间校验')
        return
    dep_datetime = datetime.strptime(f'{dep_date} {dep_time}', '%Y-%m-%d %H:%M:%S')
    flight_datetime = datetime.strptime(flight_time, '%Y-%m-%d %H:%M:%S')
    diff_minutes = abs((flight_datetime - dep_datetime).total_seconds()) / 60
    if diff_minutes > dep_diff_minutes:
        ext_msg = f'航班起飞时间差 {diff_minutes} 分钟，超过 {dep_diff_minutes} 分钟，（{flight_datetime}）'
        logger.warning(ext_msg)
        ApiCodes.BOOK_DEP_TIME_ERROR.raise_error(ext_msg=ext_msg)
    else:
        logger.info(f'航班起飞时间差 {diff_minutes} 分钟，小于 {dep_diff_minutes} 分钟。（{flight_datetime}）')


def run_book(params: dict):
    from commons.extensions.logger_extras import log_uid, log_server_name

    result = {
        'task_info': copy.deepcopy(params),
        'data': None,
        'error': {'code': ApiCodes.UNKNOWN.value, 'message': ApiCodes.UNKNOWN.label},
    }
    airline_code = params['airline_code']
    account_name = get_fixed_account(
        airline_code=airline_code,
        dep_airport_code=params['dep_airport_code'],
        arr_airport_code=params['arr_airport_code'],
    )
    account = get_account(airline_code=airline_code, account_name=account_name)
    sv = AgentService(username=account['username'], password=account['password'], airline_code=airline_code)
    base_float = params.get('base_float', 0)
    try:

        if not log_server_name.get():
            log_server_name.set('vz_crawler')
        log_uid.set(params['unique_id'])

        sv.set_proxy()
        sv.client.session.timeout = 180
        sv.login()
        for _ in range(params.get('auto_try_times', settings.SCAN_TIMES)):
            if _ > 0:
                check_resp = callback_service.start_check(params=params, default_task_type='book')
                if check_resp['code'] != ApiCodes.SUCCESS.value:
                    result['error']['code'] = check_resp['code']
                    result['error']['message'] = check_resp['message']
                    logger.warning(f'再次检查失败 {check_resp["message"]}, 停止任务')
                    return result

            search_rs = sv.search(
                dep=params['dep_airport_code'],
                arr=params['arr_airport_code'],
                date=params['dep_date'],
                adult=params['adult'],
                child=params['child'],
                infant=params['infant'],
                currency_code=params['currency_code'],
            )

            if not search_rs or not search_rs['results']:
                logger.warning(f'查询失败 {search_rs}')
                time.sleep(random.randint(2, 5))
                sv.back_index()
            else:
                break
        else:
            return ApiCodes.BOOK_SEARCH_FAIL.raise_error()
        # return  # test
        flight = [f for f in search_rs['results'] if f['trips'][0]['flight_nos'][0] == params['flight_no']]
        if flight:
            flight = flight[0]
            logger.info(f'预定目标航班：{flight}')
        else:
            return ApiCodes.BOOK_FLIGHT_NOT_FOUND.raise_error(ext_msg=params['flight_no'])
        if params.get('dep_time'):
            if len(params.get('dep_time').split(':')) == 2:
                params['dep_time'] = f'{params.get("dep_time")}:00'
        compare_dep_time(
            dep_date=params['dep_date'],
            dep_time=params.get('dep_time'),
            dep_diff_minutes=params.get('dep_diff_minutes'),
            flight_time=f'{flight["trips"][0]["dep_date"]} {flight["trips"][0]["dep_time"]}:00',
        )
        logger.debug(flight)
        src_total = ceil(params['src_adult_base'] + params['src_adult_tax'])
        fare_total = ceil(flight['trips'][0]['fares']['adult']['base'] + flight['trips'][0]['fares']['adult']['tax'])
        float_total = src_total + base_float
        if fare_total > float_total:
            # ext_msg = f'目标票价 {fare_total} 高于浮动 {float_total} ({src_total} + {base_float})'
            ext_msg = (
                f'新总价 {fare_total} 高于原总价 {src_total} (含损失浮动：{base_float}) 币种：{params["currency_code"]}'
            )
            logger.warning(ext_msg)
            return ApiCodes.BOOK_PRICE_ERROR.raise_error(ext_msg=ext_msg)
        logger.debug(f'src_total: {src_total}, fare_total: {fare_total}')

        logger.debug(flight)
        # data['flight'] = flight
        fare_key = flight['extra']['fare_key']
        ticket_num = int(fare_key.split(',')[0])
        if ticket_num < params['adult'] + params['child'] + params['infant']:
            # return ApiCodes.BOOK_TICKET_NOT_ENOUGH.raise_error()
            logger.warning(
                f'票数不足 {params["adult"] + params["child"] + params["infant"]} < {ticket_num}, fare_key:{fare_key}, 尝试预订'
            )
            # return ApiCodes.BOOK_TICKET_NOT_ENOUGH.raise_error()

        # 传递all_fares信息给book方法，让book方法内部处理DLX切换逻辑
        all_fares = flight['trips'][0].get('all_fares', {})
        book_rs = sv.book(
            fare_key=fare_key, passengers=params['passengers'], direct_pay=settings.OPEN_DIRECT_PAY, all_fares=all_fares
        )
        # if 'fare' not in book_rs:
        #     book_rs['fare'] = sv.get_order_info(real_pnr=book_rs['pnr'])
        # if not book_rs.get('fare', {}).get('currency'):
        #     book_rs['fare']['currency'] = params['currency_code']

        result['data'] = {'flight': flight, 'book': book_rs, 'account_name': account['username']}
        result['error']['code'] = ApiCodes.SUCCESS.value
        result['error']['message'] = ApiCodes.SUCCESS.label + '（已支付）'
    except Exception as e:
        if len(e.args) > 1:
            result['error'] = {'code': e.args[0], 'message': e.args[1]}
            logger.warning(e)
        else:
            result['error'] = {'code': ApiCodes.UNKNOWN.value, 'message': str(e)}
            logger.exception(e)
    finally:
        result['task_info']['host'] = sv.client.host
        result['task_info']['proxy'] = list(sv.client.session.proxies.values())[0] if sv.client.session.proxies else ''

    return result


def run_confirm_pay(params: dict):
    result = {
        'task_info': copy.deepcopy(params),
        'data': None,
        'error': {'code': ApiCodes.UNKNOWN.value, 'message': ApiCodes.UNKNOWN.label},
    }
    airline_code = params['airline_code']
    account_name = params['account_name']
    account = get_account(airline_code=airline_code, account_name=account_name)
    sv = AgentService(username=account['username'], password=account['password'], airline_code=airline_code)
    try:
        sv.set_proxy()
        sv.client.session.timeout = 120
        sv.login()
        pay_result = sv.confirm_pay(
            order_no=params['order_no'],
            real_pnr=params['real_pnr'],
            currency_code=params['currency_code'],
            total_price=params['total_price'],
            dep_date=params.get('dep_date'),
            dep_time=params.get('dep_time'),
            dep_diff_minutes=params.get('dep_diff_minutes'),
        )
        result['data'] = pay_result
        result['error']['code'] = ApiCodes.SUCCESS.value
        result['error']['message'] = ApiCodes.SUCCESS.label + '（已支付）'
    except Exception as e:
        result['error'] = {'code': ApiCodes.UNKNOWN.value, 'message': str(e)}
        logger.exception(e)
    finally:
        result['task_info']['host'] = sv.client.host
        result['task_info']['proxy'] = list(sv.client.session.proxies.values())[0] if sv.client.session.proxies else ''
    return result


def run_scan_book(params: dict):
    result = {
        'task_info': copy.deepcopy(params),
        'data': None,
        'error': {'code': ApiCodes.UNKNOWN.value, 'message': ApiCodes.UNKNOWN.label},
    }
    vj_search = vj_search_service.VJSearchService()
    search_params = {
        "unique_id": params['unique_id'],
        "dep_airport_code": params['dep_airport_code'],
        "arr_airport_code": params['arr_airport_code'],
        "dep_date": params['dep_date'],
        "return_date": "",
        "trip_type": "ow",
        "airline_code": params['airline_code'],
        "task_key": f"{params['airline_code']}-{params['dep_airport_code']}-{params['arr_airport_code']}-{params['dep_date']}",
        "currency_code": params['currency_code'],
        "adult": params['adult'],
        "child": params['child'],
        "infant": params['infant'],
    }
    flight = None
    has_lower = False
    for _ in range(params.get('auto_try_times', settings.SCAN_TIMES)):
        try:
            # VJ、VZ 用统一的代理锁，所以的airline_code都用VJ
            vj_search.set_proxy(proxy_group='default', airline_code='VJ')
            search_result = vj_search.run_search(search_params)
            logger.debug(search_result)
            # 查询失败
            if search_result['error']['code'] != ApiCodes.SUCCESS.value:
                result['error'] = search_result['error']
                logger.warning(f'查询失败 {search_result["error"]}')
                continue

            flight = [
                f for f in search_result['data']['results'] if f['trips'][0]['flight_nos'][0] == params['flight_no']
            ]
            if not flight:
                result['error']['code'] = ApiCodes.BOOK_FLIGHT_NOT_FOUND.value
                result['error']['message'] = ApiCodes.BOOK_FLIGHT_NOT_FOUND.label
                continue

            flight = flight[0]
            if ceil(flight['trips'][0]['fares']['adult']['base']) < ceil(params['src_adult_base']):
                has_lower = True
                break
            else:
                result['error']['code'] = ApiCodes.BOOK_SCAN_NO_LOWER.value
                result['error']['message'] = (
                    ApiCodes.BOOK_SCAN_NO_LOWER.label + f'余票:{flight["trips"][0]["fares"]["adult"]["quantity"]}'
                )
        finally:
            logger.info(f'无低价，1秒后重试')
            time.sleep(1)
    logger.debug(flight)
    # 票价低于上次预订，则尝试预订更低价格
    if has_lower:
        result = run_verify_book(params)
    # else:
    #     result['error']['code'] = ApiCodes.BOOK_SCAN_NO_LOWER.value
    #     result['error']['message'] = (
    #         ApiCodes.BOOK_SCAN_NO_LOWER.label + f'余票:{flight["trips"][0]["fares"]["adult"]["quantity"]}'
    #     )
    return result


# def run_hood_book(params: dict):
#     result = {
#         'task_info': copy.deepcopy(params),
#         'data': None,
#         'error': {'code': ApiCodes.UNKNOWN.value, 'message': ApiCodes.UNKNOWN.label},
#     }
#     vz_search = vj_search_service.VJSearchService()
#     search_params = {
#         "unique_id": params['unique_id'],
#         "dep_airport_code": params['dep_airport_code'],
#         "arr_airport_code": params['arr_airport_code'],
#         "dep_date": params['dep_date'],
#         "return_date": "",
#         "trip_type": "ow",
#         "airline_code": params['airline_code'],
#         "task_key": f"{params['airline_code']}-{params['dep_airport_code']}-{params['arr_airport_code']}-{params['dep_date']}",
#         "currency_code": params['currency_code'],
#         "adult": params['adult'],
#         "child": params['child'],
#         "infant": params['infant'],
#     }
#     flight = None
#     has_lower = False
#     end_time = datetime.now() + timedelta(minutes=params['keep_minutes'] if params['keep_minutes'] else 5)

#     while datetime.now() <= end_time:
#         logger.info(f'开始压位出票，当前时间：{datetime.now()}, 结束时间：{end_time}')
#         try:
#             vz_search.set_proxy(proxy_group='default', airline_code='VZ')
#             search_result = vz_search.run_search(search_params)
#             logger.debug(search_result)
#             # 查询失败
#             if search_result['error']['code'] != ApiCodes.SUCCESS.value:
#                 result['error'] = search_result['error']
#                 logger.warning(f'查询失败 {search_result["error"]}')
#                 continue

#             flight = [
#                 f for f in search_result['data']['results'] if f['trips'][0]['flight_nos'][0] == params['flight_no']
#             ]
#             if not flight:
#                 result['error']['code'] = ApiCodes.BOOK_FLIGHT_NOT_FOUND.value
#                 result['error']['message'] = ApiCodes.BOOK_FLIGHT_NOT_FOUND.label
#                 continue

#             flight = flight[0]
#             if ceil(flight['trips'][0]['fares']['adult']['base']) <= ceil(params['src_adult_base']) + 1:
#                 has_lower = True
#                 break
#             else:
#                 result['error']['code'] = ApiCodes.BOOK_SCAN_NO_LOWER.value
#                 result['error']['message'] = (
#                     ApiCodes.BOOK_SCAN_NO_LOWER.label + f'余票:{flight["trips"][0]["fares"]["adult"]["quantity"]}'
#                 )
#             logger.info(
#                 f'当前票价：{ceil(flight["trips"][0]["fares"]["adult"]["base"])}, 目标票价：<= {ceil(params["src_adult_base"])}'
#             )

#         finally:
#             logger.info(f'无低价，1秒后重试')
#             time.sleep(1)
#     logger.debug(flight)
#     # 票价低于上次预订，则尝试预订更低价格
#     if has_lower:
#         logger.info(f'找到符合条件的航班，开始预订 {flight}')
#         result = run_verify_book(params)
#     else:
#         logger.info(f'压位出票失败，结束')

#     return result


def should_use_dlx_cabin(passengers: List[Any], all_fares: dict = None) -> bool:
    """
    判断是否应该使用打包舱位
    条件：
    1. 所有乘客购买的行李公斤数一致
    2. 该公斤数对应的舱位在all_fares中存在
    3. 该舱位余票数量足够满足所有乘客

    Args:
        passengers: 乘客列表
        all_fares: 所有舱位价格信息，key为免费托运公斤数(如"20kg")
    """
    if not passengers:
        return False

    # 检查所有乘客是否都有行李，并收集行李重量
    baggage_weights = []
    for passenger in passengers:
        baggages = passenger.get('baggages', [])
        if not baggages:
            return False

        # 检查是否有行李
        passenger_baggage_weight = None
        for baggage in baggages:
            if baggage and baggage.get('aux_type') == 'baggage':
                passenger_baggage_weight = baggage.get('weight')
                break

        if passenger_baggage_weight is None:
            return False

        baggage_weights.append(passenger_baggage_weight)

    # 检查所有乘客的行李重量是否一致
    if len(set(baggage_weights)) != 1:
        return False  # 行李重量不一致

    # 获取统一的行李重量，转换为整数以匹配all_fares的key格式
    common_baggage_weight = baggage_weights[0]
    kg_key = f"{int(common_baggage_weight)}kg"

    # 检查该公斤数对应的舱位是否存在于all_fares中
    if not all_fares or kg_key not in all_fares:
        return False

    # 检查该舱位余票是否足够
    target_fare = all_fares[kg_key]
    adult_quantity = target_fare.get("adult", {}).get("quantity", 0)

    # 计算需要的票数（成人+儿童，婴儿通常不占座）
    required_tickets = len([p for p in passengers if p.get('passenger_type') in ['adult', 'child']])

    if adult_quantity < required_tickets:
        logger.warning(f"{kg_key}舱位余票不足: 需要{required_tickets}张，余票{adult_quantity}张")
        return False

    return True
