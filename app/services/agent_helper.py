from datetime import datetime, timedelta, timezone
from math import ceil

import re
from typing import Any, Tuple, Union, Dict, List, Optional
from urllib.parse import urlencode

from loguru import logger
import orjson


import re
from bs4 import BeautifulSoup

from app.clients import vz_consts
from commons.consts.api_codes import ApiCodes


def get_form_data(html: Union[str, BeautifulSoup], ignore_value_check: bool = False):
    if isinstance(html, str):
        html = BeautifulSoup(html, 'html.parser')
    root_dom = html
    # 获取所有input的name和value，并拼装成字典
    data = {}
    for input_tag in root_dom.find_all('input'):
        if 'name' not in input_tag.attrs:
            # logger.debug(f'跳过没有name的input标签: {input_tag}')
            continue
        if 'value' not in input_tag.attrs:
            # logger.debug(f'跳过没有value的input标签: {input_tag}')
            if ignore_value_check:
                data[input_tag['name']] = None
            continue
        data[input_tag['name']] = input_tag['value']
    # 获取所有select对象，并获取其selected的option的value
    for select_tag in root_dom.find_all('select'):
        if 'name' not in select_tag.attrs:
            # logger.debug(f'跳过没有name的select标签: {select_tag}')
            continue
        for option_tag in select_tag.find_all('option'):
            if option_tag.has_attr('selected'):
                data[select_tag['name']] = option_tag['value']
                break
        else:
            data[select_tag['name']] = ''
    return data


def get_tab_id(root_dom: BeautifulSoup, date: str):
    tab_dom = root_dom.find('ul', {'class': 'tabs_ribbon'})
    # 遍历ul下所有a标签
    for a_tag in tab_dom.find_all('a'):
        # logger.debug(f'遍历日期: {date}, a_tag: {a_tag}')
        # text内容包含date的a标签
        if date in a_tag.text:
            # 获取a标签的id属性值
            tab_id = a_tag['id']
            # 返回id值
            logger.debug(f'选中日期: {date}, tab_id: {tab_id}')
            return tab_id.replace('gridTravelOptDepTab', '')
    return None


def parse_flight(html: str, date: str, passenger_num: int, airline_code: str, cabin_level: str = "Eco"):
    result = {"results": [], "exchange": {"currency_code": "THB", "rates": {}}}
    root_dom = BeautifulSoup(html, 'html.parser')

    tab_id = get_tab_id(root_dom, date)

    tab_dom = root_dom.find('div', {'id': f'gridTravelOptDepPane{tab_id}', 'class': 'pane'})
    # logger.debug(f'tab_dom: {len(tab_dom)}')
    # logger.debug(tab_dom)
    trip_dom = tab_dom.find('table', {'class': 'FlightsGrid'})
    # logger.debug(f'trip_dom: {len(trip_dom)}')
    # logger.debug(trip_dom)
    # 取出 trips_dom 中所有table对象，注意只取第一级，多级嵌套的table会被忽略
    baggage_info = get_baggage_info(root_dom=root_dom, cabin_level=cabin_level)
    logger.debug(f'baggage_info {baggage_info}')
    for flight_tag in trip_dom.find_all('tr', {'id': re.compile('gridTravelOptDep.*')}):
        # logger.debug('遍历航班')
        # logger.debug(flight_tag)
        tr_tag = flight_tag.find_all('tr')
        # for tr in tr_tag:
        # logger.debug(f'航班下tr: {tr}')

        flight_info = get_flight_info(tr_tag[0], airline_code=airline_code)
        if not flight_info:
            continue

        # 获取所有舱位的价格信息，传入root_dom以提取免费托运公斤数
        eco_fare_info = get_fare_info(tr_tag[1], passenger_num=passenger_num, cabin_level="Eco", root_dom=root_dom)
        dlx_fare_info = get_fare_info(tr_tag[1], passenger_num=passenger_num, cabin_level="Deluxe", root_dom=root_dom)
        skyboss_fare_info = get_fare_info(
            tr_tag[1], passenger_num=passenger_num, cabin_level="SkyBoss", root_dom=root_dom
        )
        business_fare_info = get_fare_info(
            tr_tag[1], passenger_num=passenger_num, cabin_level="Business", root_dom=root_dom
        )

        # 根据请求的舱位级别选择主要的fare_info
        if cabin_level == "Deluxe" and dlx_fare_info:
            fare_info = dlx_fare_info
        elif cabin_level == "SkyBoss" and skyboss_fare_info:
            fare_info = skyboss_fare_info
        elif cabin_level == "Business" and business_fare_info:
            fare_info = business_fare_info
        elif eco_fare_info:
            fare_info = eco_fare_info
        else:
            continue

        flight_info['extra']['fare_key'] = fare_info['fare_key']
        # result['results'].append(flight_info)
        segment_dict = [
            {
                "segment_index": 0,
                "flight_number": flight_info['flight_no'][2:],
                "flight_no": flight_info['flight_no'],
                "dep_airport_code": flight_info['dep_airport_code'],
                "arr_airport_code": flight_info['arr_airport_code'],
                "dep_date": flight_info['dep_date'],
                "dep_time": flight_info['dep_time'],
                "arr_date": flight_info['arr_date'],
                "arr_time": flight_info['arr_time'],
                "stop_times": 0,
                "cabin": fare_info['cabin'],
                "stops": [
                    # {
                    #     "stop_index": 1,
                    #     "stop_airport_code": "",
                    #     "dep_date": "",
                    #     "dep_time": "",
                    #     "arr_date": "",
                    #     "arr_time": "",
                    # }
                ],
                "extra": flight_info['extra'],
            }
        ]

        # 构建all_fares字典，用免费托运公斤数做key
        all_fares = {}

        # 收集所有未售完的舱位信息
        fare_infos = [
            ("Eco", eco_fare_info),
            ("Deluxe", dlx_fare_info),
            ("SkyBoss", skyboss_fare_info),
            ("Business", business_fare_info),
        ]

        for cabin_level, fare_info_item in fare_infos:
            if fare_info_item:
                # 除了ECO舱位外，其他舱位如果取不到公斤数（为0）则不存入all_fares
                # 这样确保0kg永远是最低价ECO舱位，避免高舱位取不到公斤数时错当成最低价
                if cabin_level != "Eco" and fare_info_item['free_checked_baggage_kg'] == 0:
                    continue

                # 使用免费托运公斤数作为key
                kg_key = f"{fare_info_item['free_checked_baggage_kg']}kg"
                all_fares[kg_key] = {
                    "adult": fare_info_item['adult'],
                    "child": fare_info_item['child'],
                    "infant": fare_info_item['infant'],
                    "fare_key": fare_info_item['fare_key'],
                    "cabin": fare_info_item['cabin'],
                    "free_checked_baggage_kg": fare_info_item['free_checked_baggage_kg'],
                }

        # 确定最低价格的舱位作为fares（比较所有舱位价格）
        available_fares = [fare_info for cabin_level, fare_info in fare_infos if fare_info is not None]
        if available_fares:
            # 选择成人总价最低的舱位
            fare_info = min(available_fares, key=lambda x: x['adult']['total'])
        else:
            continue  # 没有可用舱位，跳过这个航班

        # 更新flight_info的fare_key为最低价格舱位的fare_key
        flight_info['extra']['fare_key'] = fare_info['fare_key']

        # fares永远存放最低价格的舱位
        fare_dict = {"adult": fare_info['adult'], "child": fare_info['child'], "infant": fare_info['infant']}

        across_days = (
            datetime.strptime(flight_info['arr_date'], '%Y-%m-%d')
            - datetime.strptime(flight_info['dep_date'], '%Y-%m-%d')
        ).days

        flight_dict = {
            "trip_type": "ow",
            "trips": [
                {
                    "airline_codes": [seg['flight_no'][:2] for seg in segment_dict],
                    "cabin_codes": [seg['cabin']['code'] for seg in segment_dict],
                    "trip_index": 0,
                    "flight_nos": [seg['flight_no'] for seg in segment_dict],
                    "dep_airport_code": flight_info['dep_airport_code'],
                    "arr_airport_code": flight_info['arr_airport_code'],
                    "dep_date": flight_info['dep_date'],
                    "dep_time": flight_info['dep_time'],
                    "arr_date": flight_info['arr_date'],
                    "arr_time": flight_info['arr_time'],
                    "stop_times": 0,
                    "across_days": across_days,
                    "segments": segment_dict,
                    "fares": fare_dict,
                    "all_fares": all_fares,
                    "includes": baggage_info['baggage'],
                }
            ],
            "extra": flight_info['extra'],
        }
        result['results'].append(flight_dict)
    logger.debug(result)
    return result


def get_flight_info(tr_dom: BeautifulSoup, airline_code: str):
    flight_info = {}

    tds = tr_dom.find_all('td')

    # 提取信息
    dep_date = tds[0].text.strip().split()[0]  # "28/02/2025"
    logger.debug(tds[1].text.strip())
    dep_time, dep_airport_code = re.split(r'[\s\r]+', tds[1].text.strip())[:2]  # "13:25 BKK"
    dep_airport_code = dep_airport_code.upper()[:3]  # "BKK"
    arr_time, arr_airport_code = re.split(r'[\s\r]+', tds[2].text.strip())[:2]  # "14:50 CEI"
    arr_airport_code = arr_airport_code.upper()[:3]  # "CEI"
    logger.debug(tds[3].text.strip())
    # exit(0)
    flight_no = tds[3].find("span", class_=f"airline{airline_code}").text.strip()  # "VZ132"
    flight_number = flight_no  # 这里通常是完整航班号
    aircraft = ""  # 由于 HTML 里没有飞机型号，这里留空
    # cabin_level, cabin_code = "经济舱", "Y"  # 经济舱通常用 "Y" 表示

    # 计算到达日期（假设无跨天）
    dep_date_obj = datetime.strptime(dep_date, "%d/%m/%Y")
    dep_date = dep_date_obj.strftime("%Y-%m-%d")
    arr_date_obj = dep_date_obj  # 无过夜航班，默认到达日期相同
    arr_date = arr_date_obj.strftime("%Y-%m-%d")

    # 构造 JSON 结构
    flight_info = {
        "segment_index": 0,
        "flight_number": flight_number,
        "flight_no": flight_no,
        "dep_airport_code": dep_airport_code,
        "arr_airport_code": arr_airport_code,
        "dep_date": dep_date,
        "dep_time": dep_time,
        "arr_date": arr_date,
        "arr_time": arr_time,
        "stop_times": 0,
        # "cabin": {"cabin_class": cabin_level, "code": cabin_code, "name": "经济舱", "desc": "经济舱"},
        "stops": [],
        "extra": {"aircraft_code": aircraft},
    }

    # 输出 JSON
    logger.debug(flight_info)

    return flight_info


def get_fare_info(tr_dom: BeautifulSoup, passenger_num: int, cabin_level="Eco", root_dom: BeautifulSoup = None):
    logger.debug(tr_dom)
    cabin_mapping = {"Eco": "经济舱", "Deluxe": "豪华经济舱", "SkyBoss": "商务舱", "Business": "商务舱"}

    cabin_td = tr_dom.find("td", {"data-familyid": cabin_level})
    if not cabin_td or '售完' in cabin_td.text:
        return None  # 如果找不到对应的舱位，返回 None

    def extract_hidden_value(field_id):
        tag = cabin_td.find("input", {"id": field_id})
        if tag:
            tag["value"] = tag["value"].replace(',', '')
            return float(tag["value"].split(" ")[0]) if tag else 0.0
        return None

    base_fare = extract_hidden_value("fare")
    fare_taxes = extract_hidden_value("fare_taxes")
    charge_taxes = extract_hidden_value("charge_taxes")
    charges = extract_hidden_value("charges")
    # total_fare = extract_hidden_value("total_fare")
    total_complete_charges = extract_hidden_value("total_complete_charges")
    # dep_time = cabin_td.find("input", {"id": "depTime"})["value"]
    fare_key = cabin_td.find("input", {"name": "gridTravelOptDep"})["value"]
    availability = int(fare_key.split(",")[0])
    cabin_code = fare_key.split(",")[1].split("_")[0].upper()

    tax_total = (fare_taxes + charge_taxes + charges) / passenger_num
    if round(total_complete_charges, 2) < round(base_fare + tax_total, 2):
        logger.warning(f"total_complete_charges {total_complete_charges} < {base_fare+tax_total}")
        return None

    # 提取免费托运行李重量
    free_checked_baggage_kg = 0
    if root_dom:
        free_checked_baggage_kg = extract_free_checked_baggage_weight(root_dom, cabin_level)

    fare_info = {
        "cabin": {
            "cabin_class": cabin_level,
            "code": cabin_code,
            "name": cabin_mapping.get(cabin_level, "未知舱位"),
            "desc": cabin_mapping.get(cabin_level, "未知舱位"),
        },
        "adult": {"base": base_fare, "tax": tax_total, "total": total_complete_charges, "quantity": availability},
        "child": {"base": base_fare, "tax": tax_total, "total": total_complete_charges, "quantity": availability},
        "infant": {"base": base_fare, "tax": tax_total, "total": total_complete_charges, "quantity": 0},
        "fare_key": fare_key,
        "free_checked_baggage_kg": free_checked_baggage_kg,  # 新增免费托运公斤数
    }
    logger.debug(fare_info)

    return fare_info


def extract_weight(text):
    """从文本中提取重量信息，返回整数或 None"""
    match = re.search(r"(\d+)\s*公斤", text)
    return int(match.group(1)) if match else None


def extract_free_checked_baggage_weight(root_dom: BeautifulSoup, cabin_level="Eco"):
    """
    从HTML中提取免费托运行李重量

    Args:
        root_dom: BeautifulSoup对象
        cabin_level: 舱位等级 (Eco, Deluxe, SkyBoss, Business)

    Returns:
        int: 免费托运行李重量(kg)，如果没有免费托运行李返回0
    """
    # 根据舱位等级确定对应的ID
    cabin_id_map = {
        "Eco": "ecofarerules",
        "Deluxe": "deluxefarerules",
        "SkyBoss": "skybossfarerules",
        "Business": "skybosspremierfarerules",  # Business舱位使用skybosspremier ID
    }

    fare_id = cabin_id_map.get(cabin_level)
    if not fare_id:
        return 0

    # 查找对应的舱位规则
    fare_div = root_dom.find("div", class_="fareRulesMOver", id=fare_id)
    if not fare_div:
        return 0

    # 查找包含的服务部分
    include_sections = fare_div.find_all("div", class_="fareRulesContent")

    for include_section in include_sections:
        for li in include_section.find_all("li", class_="greencheckmark"):
            text = li.get_text(strip=True)

            # 优先匹配英文托运行李描述
            if "checked baggage" in text.lower():
                # "20kgs checked baggage." -> 20
                weight_match = re.search(r"(\d+)\s*kgs?", text)
                if weight_match:
                    return int(weight_match.group(1))

    # 如果英文取不到，用中文兜底策略：取中文描述中的第一个公斤数（取消"或"字判断）
    for include_section in include_sections:
        for li in include_section.find_all("li", class_="greencheckmark"):
            text = li.get_text(strip=True)

            # 匹配中文托运行李描述，取第一个公斤数
            if "托运行李" in text:
                # "免费20公斤的托运行李" -> 20
                # "免费20公斤或25公斤的托运行李" -> 20 (取第一个)
                weight_match = re.search(r"(\d+)\s*公斤", text)
                if weight_match:
                    return int(weight_match.group(1))

    return 0  # 没有免费托运行李


def get_baggage_info(root_dom: BeautifulSoup, cabin_level="Eco"):
    baggage_info = {"baggage": {}}
    # logger.debug(root_dom)
    # logger.debug(f"{cabin_level.lower()}farerules" in root_dom)
    # 查找对应的舱位
    fare_div = root_dom.find("div", class_="fareRulesMOver", id=f"{cabin_level.lower()}farerules")
    # logger.debug(fare_div)
    if not fare_div:
        return baggage_info  # 没找到对应舱位，返回空结构

    # 解析包含的服务
    include_section = fare_div.find("div", class_="fareRulesContent")
    if include_section:
        for li in include_section.find_all("li", class_="greencheckmark"):
            text = li.get_text(strip=True)
            weight = extract_weight(text)

            if "手提行李" in text and weight:
                if 'cabin_baggage' not in baggage_info["baggage"]:
                    baggage_info["baggage"]["cabin_baggage"] = []
                baggage_info["baggage"]["cabin_baggage"].append({"weight": weight, "count": 1, "all_weight": weight})
            elif "托运行李" in text and weight:
                if 'checked_baggage' not in baggage_info["baggage"]:
                    baggage_info["baggage"]["checked_baggage"] = []
                baggage_info["baggage"]["checked_baggage"].append({"weight": weight, "count": 1, "all_weight": weight})

    return baggage_info


def get_error_msg(html):
    #  <p class='ErrorMessage'>您没有操作此功能的权限</p><input type='hidden' name='DebugID' value='C1' />
    root_dom = BeautifulSoup(html, 'html.parser')
    error_msg = root_dom.find('p', {'class': 'ErrorMessage'})
    if error_msg:
        return error_msg.text.strip()
    return ''


def get_passengers_form_data(passengers):
    data = {}
    for idx in range(len(passengers)):
        passenger = passengers[idx]
        num = idx + 1
        brithday = datetime.strptime(passenger['birthday'], '%Y-%m-%d')
        # country_code = vz_consts.COUNTRY_MAP[passenger['country']]['code']
        card_valid_date = datetime.strptime(passenger['card_valid_date'], '%Y-%m-%d')
        card_cuntry_code = vz_consts.COUNTRY_MAP[passenger['card_country']]['code']

        data[f'txtPax{num}_Gender'] = (
            passenger['sex'].capitalize()[:1] if passenger['passenger_type'] == 'adult' else 'C'
        )
        data[f'txtPax{num}_LName'] = passenger['last_name']
        data[f'txtPax{num}_FName'] = passenger['first_name']
        data[f'txtPax{num}_Addr1'] = ''
        data[f'txtPax{num}_City'] = ''
        data[f'txtPax{num}_Ctry'] = '-1'
        data[f'txtPax{num}_Prov'] = '-1'
        data[f'txtPax{num}_Phone2'] = '+8613818432182' if idx == 0 else ''
        data[f'txtPax{num}_EMail'] = '<EMAIL>' if idx == 0 else ''
        data[f'txtPax{num}_Phone1'] = ''

        if passenger['card_no']:
            data[f'txtPax{num}_DOB_Day'] = brithday.strftime('%d')
            data[f'txtPax{num}_DOB_Month'] = brithday.strftime('%m')
            data[f'txtPax{num}_DOB_Year'] = brithday.strftime('%Y')
            data[f'txtPax{num}_Passport'] = passenger['card_no']
            data[f'dlstPax{num}_PassportExpiry_Day'] = card_valid_date.strftime('%d')
            data[f'dlstPax{num}_PassportExpiry_Month'] = card_valid_date.strftime('%Y/%m')
            data[f'txtPax{num}_Nationality'] = card_cuntry_code
            data[f'txtPax{num}_PrefLanguage'] = '4'
        else:
            data[f'txtPax{num}_DOB_Day'] = ''
            data[f'txtPax{num}_DOB_Month'] = ''
            data[f'txtPax{num}_DOB_Year'] = ''
            data[f'txtPax{num}_Passport'] = ''
            data[f'dlstPax{num}_PassportExpiry_Day'] = ''
            data[f'dlstPax{num}_PassportExpiry_Month'] = ''
            data[f'txtPax{num}_Nationality'] = ''
            data[f'txtPax{num}_PrefLanguage'] = '4'
        # if passenger['passenger_type'] == 'adult':
        #     data[f'txtPax{num}_Addr1'] = ''
        #     data[f'txtPax{num}_City'] = ''
        #     data[f'txtPax{num}_Ctry'] = '-1'
        #     data[f'txtPax{num}_Prov'] = '-1'
        #     data[f'txtPax{num}_Phone2'] = '+8613818432182' if idx == 0 else ''
        #     data[f'hidPax{num}_Search'] = '-1'
        data[f'hidPax{num}_Search'] = '-1'
        # ==========
        if passenger['card_no']:
            data[f'txtPax{num}_APISDOCNATIONALITY_1'] = card_cuntry_code
            data[f'txtPax{num}_APISDOCNMBR_1'] = passenger['card_no']
            data[f'dlstPax{num}_APISDOCEXPDATE_1_Day'] = card_valid_date.strftime('%d')
            data[f'dlstPax{num}_APISDOCEXPDATE_1_Month'] = card_valid_date.strftime('%Y/%m')
            data[f'txtPax{num}_APISDOCID_1'] = '-1'
            data[f'txtPax{num}_APISID'] = '-1'
            data[f'txtPax{num}_APISPAXGROUPID'] = ''
            data[f'txtPax{num}_APISPAXID'] = ''
            data[f'txtPax{num}_APISTIMESTAMP'] = ''

    # 'txtPax1_APISDOCNATIONALITY_1': 'CHN',
    # 'txtPax1_APISDOCNMBR_1': '*********',
    # 'dlstPax1_APISDOCEXPDATE_1_Day': '19',
    # 'dlstPax1_APISDOCEXPDATE_1_Month': '2026/05',
    # 'txtPax1_APISDOCID_1': '-1',
    # 'txtPax1_APISID': '-1',
    # 'txtPax1_APISPAXGROUPID': '',
    # 'txtPax1_APISPAXID': '',
    # 'txtPax1_APISTIMESTAMP': '',
    # 'txtPax1_Gender': 'M',
    #         'txtPax1_LName': 'yang',
    #         'txtPax1_FName': 'jian',
    #         'txtPax1_Addr1': '',
    #         'txtPax1_City': '',
    #         'txtPax1_Ctry': '-1',
    #         'txtPax1_Prov': '-1',
    #         'txtPax1_EMail': '<EMAIL>',
    #         'txtPax1_DOB_Day': '11',
    #         'txtPax1_DOB_Month': '05',
    #         'txtPax1_DOB_Year': '1989',
    #         'txtPax1_Phone2': '+8613551577877',
    #         'txtPax1_Phone1': '',
    #         'txtPax1_Passport': '********',
    #         'dlstPax1_PassportExpiry_Day': '06',
    #         'dlstPax1_PassportExpiry_Month': '2026/01',
    #         'txtPax1_Nationality': 'CHN',
    #         'txtPax1_PrefLanguage': '4',
    #         'hidPax1_Search': '-1',
    return data


def get_addons_form_data(passengers: List[Any], addons: dict):
    data = [
        ('SesID', ''),
        ('button', 'continue'),
        ('m1th', addons.get('m1th', '2')),
        ('m1p', '1'),
        ('ctrSeatAssM', '1'),
        ('ctrSeatAssP', str(len(passengers))),
        # ('shopAmt', addons.get('shopAmt', '1')),
    ]
    shop_amt = addons.get('shopAmt', '0')
    exclude_ids = []
    baggage_map = {}
    # 循环乘客节点
    for idx in range(len(passengers)):
        num = idx + 1
        passenger = passengers[idx]
        # 这两个字段是选座
        data.append((f'm1p{num}', ''))
        data.append((f'm1p{num}rpg', ''))
        if (
            passenger.get('baggages')
            and passenger.get('baggages')[0].get('aux_type') == 'baggage'
            and passenger.get('baggages')[0].get('from_select_id')
        ):
            select_id = passenger.get('baggages')[0].get('from_select_id')
            values = passenger.get('baggages')[0].get('from_select_value').split('/')
            baggage_map[select_id] = values
            shop_amt = '1'
            # baggage_map[f'hidPaxItem{values[2]}'] = values[3]
            # data.append((values[0], values[1]))
            # data.append((f'hidPaxItem{values[2]}', values[3]))
            # data.append(('-1', '-1'))
            # exclude_ids.append(values[0])
            # exclude_ids.append(f'hidPaxItem{values[2]}')
    logger.debug(f'baggage_map {baggage_map}')
    # 辅营内容从前个页面取出，避免不同线路或航班出现不一样的情况
    for k, v in addons.items():
        # if k in exclude_ids:
        #     continue
        if k.startswith('lstPaxItem'):
            if k in baggage_map:
                data.append((k, baggage_map[k][1]))
                # data.append((k, '-1'))
                data.append((f'hidPaxItem:{baggage_map[k][2]}', baggage_map[k][3]))
                data.append(('-1', '-1'))
            else:
                data.append((k, '-1'))
                data.append(('-1', '-1'))
            continue
        if k.startswith('hidPaxItem'):
            # if k in baggage_map:
            #     data.append((k, baggage_map[k]))
            # else:
            data.append(('shpsel', ''))
            data.append((k, v))
            continue
    data.append(('shopAmt', shop_amt))

    return data


def get_confirm_form_data(passengers):
    data = {}
    for idx in range(len(passengers)):
        passenger = passengers[idx]
        num = idx + 1
        # brithday = datetime.strptime(passenger['birthday'], '%Y-%m-%d')
        # country_code = vz_consts.COUNTRY_MAP[passenger['country']]['code']
        # card_valid_date = datetime.strptime(passenger['card_valid_date'], '%Y-%m-%d')
        data[f'txtPax{num}_Gender'] = passenger['sex'].capitalize()[:1]
        data[f'txtPax{num}_LName'] = passenger['last_name']
        data[f'txtPax{num}_FName'] = passenger['first_name']
        data[f'txtPax{num}_PrefLanguage'] = '4'
        data[f'dlstPax{num}_Search'] = '-1'
    return data


def parse_book_result(html, airline_code: str):
    result = {"passengers": [], "contact": {}, "pnr": "", "dep_expire_time": None, "expire_time": None, "flights": []}
    root_dom = BeautifulSoup(html, 'html.parser')
    pnr = root_dom.find('span', {'class': 'ResNumber'}).get_text().strip().upper()
    result['pnr'] = pnr
    result.update(get_order_expire(root_dom=root_dom))
    # table_tag = root_dom.find('table')
    for table_tag in root_dom.find_all('table'):
        logger.debug(f'table {table_tag}')
        if '名称:' in table_tag.text:
            logger.debug(f'try parse agent')
            result['contact'] = get_book_agent(table_tag)
        elif '中间名和姓:' in table_tag.text:
            logger.debug(f'try parse passengers')
            result['passengers'].append(get_book_passenger(table_tag))
        elif '航班明細' in table_tag.text:
            logger.debug(f'try parse flight')
            result['flights'] = get_book_flights(table_tag, airline_code=airline_code)
        # elif '金额:' in table_tag.text:
        #     logger.debug(f'try parse fare')
        #     result['fare'] = get_book_fare(table_tag)

    logger.debug(f'result {result}')
    return result


def get_book_agent(table_tag: BeautifulSoup):
    agent_info = {}
    tr_tags = table_tag.find_all("tr")
    label_span = tr_tags[0].find("span", class_="FormLabel")  # 获取标签
    value_span = label_span.find_next_sibling("span").get_text().strip() if label_span else None  # 获取值
    agent_info['agent'] = value_span

    label_span = tr_tags[1].find("span", class_="FormLabel")  # 获取标签
    value_span = label_span.find_next_sibling("span").get_text().strip() if label_span else None  # 获取值
    agent_info['phone'] = value_span

    label_span = tr_tags[2].find("span", class_="FormLabel")  # 获取标签
    value_span = label_span.find_next_sibling("span").get_text().strip() if label_span else None  # 获取值
    agent_info['email'] = value_span

    return agent_info


def get_book_passenger(table_tag: BeautifulSoup):
    data = get_form_data(table_tag)
    passenger = {}
    for k, v in data.items():
        # logger.debug(f'k {k} v {v}')
        if k.endswith('_LName'):
            passenger['last_name'] = v
        elif k.endswith('_FName'):
            passenger['first_name'] = v
        elif k.endswith('_Gender'):
            if v == 'C':
                passenger['passenger_type'] = 'child'
            else:
                passenger['passenger_type'] = 'adult'
            passenger['sex'] = v
    for span_tag in table_tag.find_all("span", class_="FormLabel"):
        if '护照号码' in span_tag.text:  # 获取护照号码
            next_span = span_tag.find_next_sibling("span")
            passenger['card_no'] = next_span.text.strip()
        elif '护照期限' in span_tag.text:  # 获取护照号码
            next_span = span_tag.find_next_sibling("span")
            # 23/01/2030 Wed
            if next_span is None:
                passenger['card_valid_date'] = ''
            else:
                passenger['card_valid_date'] = datetime.strptime(next_span.text.strip(), '%d/%m/%Y %a').strftime(
                    '%Y-%m-%d'
                )

    return passenger


def get_order_expire(root_dom):
    result = {'dep_expire_time': None, 'expire_time': None}
    # 找到包含时间的 <h1> 元素
    time_text = None
    for h1 in root_dom.find_all("h1"):
        if "自动被取消" in h1.text:
            time_text = h1.text
            break
    if not time_text:
        return result

    # 提取日期、时间和时区信息
    match = re.search(r"(\d{2}/\d{2}/\d{4}) \w+ (\d{2}:\d{2}) \(GMT([+-]\d+)\)", time_text)
    if not match:
        return None
    date_str, time_str, gmt_offset = match.groups()
    local_dt_str = f"{date_str} {time_str}"

    # 解析原始时间
    offset_hours = int(gmt_offset)  # GMT 偏移量
    local_tz = timezone(timedelta(hours=offset_hours))
    local_dt = datetime.strptime(local_dt_str, "%d/%m/%Y %H:%M").replace(tzinfo=local_tz)

    # 转换为当前系统时区
    current_tz = datetime.now().astimezone().tzinfo  # 获取本机当前时区
    converted_dt = local_dt.astimezone(current_tz)

    result["dep_expire_time"] = local_dt.strftime("%Y-%m-%d %H:%M:%S %Z")
    result["expire_time"] = converted_dt.strftime("%Y-%m-%d %H:%M:%S")
    return result


def get_book_flights(table_tag, airline_code: str):
    flight_info = []

    # 查找所有航班行
    for row in table_tag.find_all("tr", class_="gridFlightEven"):
        # 获取起飞日期
        date_text = row.find_all("td", class_="SegInfo")[0].get_text(strip=True)
        flight_date = datetime.strptime(date_text, "%d/%m/%Y %a").strftime("%Y-%m-%d")  # 转换格式

        # 获取起飞时间和机场
        departure_info = row.find_all("td", class_="SegInfo")[1].get_text(" ", strip=True)
        dep_time, dep_airport = departure_info.split()[:2]

        # 获取降落时间和机场
        arrival_info = row.find_all("td", class_="SegInfo")[2].get_text(" ", strip=True)
        arr_time, arr_airport = arrival_info.split()[:2]

        # 获取航班号
        flight_no = row.find("span", class_=f"airline{airline_code}").get_text(strip=True)

        # 获取飞行时长并转换为分钟
        duration_text = (
            row.find_all("td", class_="SegInfo")[3]
            .get_text(strip=True)
            .split("Operated by")[0]
            .strip()
            .split(flight_no)[-1]
        )
        hours = 0
        minutes = 0
        if "h" in duration_text:
            hours = int(duration_text.split("h")[0].strip())
            duration_text = duration_text.split("h")[1].strip()
        if "m" in duration_text:
            minutes = int(duration_text.split("m")[0].strip())
        duration_minutes = hours * 60 + minutes

        # 组装结果
        flight_info.append(
            {
                "airline_code": flight_no[:2],
                "flight_no": flight_no,
                "dep_airport_code": dep_airport,
                "arr_airport_code": arr_airport,
                "flight_date": flight_date,
                "dep_time": dep_time,
                "arr_time": arr_time,
                "duration_minutes": duration_minutes,
            }
        )

    return flight_info


def get_book_fare(payment_html: str):
    root_dom = BeautifulSoup(payment_html, 'html.parser')
    # table_tag = root_dom.find('table', class_='tblLeg1Info')

    ticket_base = 0.0
    total_tax = 0.0
    compute_total = 0.0
    total_price = 0.0
    currency = None
    cabin_class = None
    cabin_code = None
    """
    <tr id='Leg1BookingSummary' style=''>
        <td>
            <span class='label'>票价: </span><span id='Leg1BSFare'>53.10</span> <span>CNY</span><br />
            <div>
                <span class='label'>计费: </span><span id='Leg1BSCharges'>487.57</span> <span>CNY</span><br />
                <span class='label'>税务: </span><span id='Leg1BSFareTax'>0.00</span> <span>CNY</span><br />
                <span class='label'>总计: </span><span id='Leg1BSTotalFare'>540.67</span> <span>CNY</span>
            </div>
        </td>
    </tr>
</table>
    """

    # 解析各项费用
    tr_dom = root_dom.find('tr', id='Leg1BookingSummary')
    result = {
        "ticket_base": 0.0,
        "total_tax": 0.0,
        "total_price": 0.0,
        "currency": None,
        "cabin_class": None,
        "cabin_code": None,
    }
    if tr_dom:
        fare_span = tr_dom.find('span', id='Leg1BSFare')
        if fare_span:
            ticket_base = float(fare_span.get_text(strip=True).replace(',', ''))
            result['ticket_base'] = round(ticket_base, 2)
            currency = fare_span.find_next('span').get_text(strip=True)
            result['currency'] = currency

        charges_span = tr_dom.find('span', id='Leg1BSCharges')
        if charges_span:
            total_tax = float(charges_span.get_text(strip=True).replace(',', ''))
            result['total_tax'] = round(total_tax, 2)

        total_price_span = tr_dom.find('span', id='Leg1BSTotalFare')
        if total_price_span:
            total_price = float(total_price_span.get_text(strip=True).replace(',', ''))
            result['total_price'] = round(total_price, 2)

    return result


def update_baggage_options(airline_code: str, html_content: str, passengers: List[Any]) -> Tuple[Dict, Dict]:
    """
    提取行李选项并映射到乘客
    返回: (去程行李映射, 返程行李映射)
    """
    soup = BeautifulSoup(html_content, 'html.parser')

    # 查找所有行李选择框
    selects = soup.select('select.lstShopSelect')

    for select in selects:
        select_id = select.get('id', '')
        options = select.find_all('option')

        from_results = {}
        back_results = {}
        has_baggage = False
        logger.debug(f'select_id {select_id}')
        logger.debug(f'options {options}')
        for option in options:
            key = option.get_text(strip=True)
            value = option.get('value', '')
            hid_pax_value = option.get('hidpaxvalue', '')  # 注意属性名可能大小写不敏感
            hid_pax_item = option.get('hidpaxitem', '')
            # key = hid_pax_value

            # 判断是否为行李选项
            is_baggage = any(x in hid_pax_value.lower() for x in ["bag", "kgs "])
            if not is_baggage:
                logger.warning(f'不是行李选项 {hid_pax_value}')
                continue

            # 过滤无效选项
            if "no, thanks" in key.lower() or "oversize" in key.lower():
                logger.warning(f'无效选项 {key}')
                continue

            # 构建选项标识
            option_value = f"{key}/{value}/{hid_pax_item}/{hid_pax_value}"
            has_baggage = True

            # 分类存储
            if hid_pax_value.startswith('1'):
                if select_id not in from_results:
                    from_results[select_id] = []
                from_results[select_id].append(option_value)
            elif hid_pax_value.startswith('2'):
                if select_id not in back_results:
                    back_results[select_id] = []
                back_results[select_id].append(option_value)
            logger.debug(f'找到行李选项 option_value {option_value}')

        logger.debug(f'from_results {from_results}')
        logger.debug(f'back_results {back_results}')

        def set_baggage_option(passenger, num, results, trip_prefix):
            logger.debug(f'尝试为 {passenger} 添加行李')
            for bag in passenger.get('baggages', []):
                if not bag:
                    continue
                if bag.get('aux_type') == 'baggage':
                    bag_str = f' {int(bag["weight"])}kgs'
                    if airline_code == 'VZ':
                        bag_str = f'{bag_str} (VZ)'

                    # bag_str = f'{int(bag["weight"])}kgs'
                    if not bag.get(f'{trip_prefix}_select_id'):
                        for select_id, option_values in results.items():
                            if not select_id.startswith(f'lstPaxItem:-{num}:'):
                                logger.warning(f'select_id {select_id} 不匹配 {num}')
                                continue
                            for option_value in option_values:
                                hid_pax_value = option_value.split('/')[-1]

                                if bag_str in hid_pax_value:
                                    logger.info(
                                        f'将行李 {bag_str} 加入到 select_id {select_id} hid_pax_value {hid_pax_value}'
                                    )
                                    bag[f'{trip_prefix}_select_id'] = select_id
                                    bag[f'{trip_prefix}_select_value'] = option_value
                                    break
                                else:
                                    logger.warning(
                                        f'hid_pax_value "{hid_pax_value}" bag_str "{bag_str}" select_id "{select_id} {num}"'
                                    )
                                    logger.warning(f'bag_str in hid_pax_value {bag_str in hid_pax_value}')
                                    logger.warning(select_id.startswith(f'lstPaxItem:-{num}:'))
                            else:
                                logger.debug(f'bag {bag}')
                                logger.error(f'没有找到 {bag_str} 的行李选项, results: {results}')
                                # ApiCodes.BOOK_BAGGAGE_ERROR.raise_error(ext_msg=f'没有找到{bag_str}的行李选项')

        # 映射到乘客
        if has_baggage:
            logger.info(f'行李信息 {from_results}')
            # 分配去程行李
            real_idx = 0
            for idx in range(len(passengers)):
                passenger = passengers[idx]
                if not passenger.get('baggages'):
                    logger.info(f'乘客未购买行李 {passenger}')
                    continue
                real_idx = idx + 1
                if from_results:
                    set_baggage_option(passenger, real_idx, from_results, 'from')
                if back_results:
                    set_baggage_option(passenger, real_idx, back_results, 'back')

    for p in passengers:
        if p.get('baggages'):
            for b in p['baggages']:
                if b and 'from_select_id' not in b and 'back_select_id' not in b:
                    logger.error(f'没有找到行李选项, passenger: {p}')
                    ApiCodes.BOOK_BAGGAGE_ERROR.raise_error(ext_msg='没有找到行李选项')

    # exit(0)
    return passengers


def extract_baggage_price_from_select_value(select_value: str) -> float:
    """
    从*_select_value中提取行李价格
    例如：1|NA|3|68189641|55.3|False|3|Bag 20kgs||NA||0|Baggage 20kgs|55.30 CNY|55.3|4.42|0|0|1
    返回价格：55.3

    Args:
        select_value: 行李选择值字符串，可能为None（无行李情况）
    Returns:
        float: 提取的价格，无法提取时返回0.0
    """
    # 处理None输入（无行李情况）
    if select_value is None:
        return 0.0

    try:
        parts = select_value.split('|')
        if len(parts) >= 5:
            # 第5个部分（索引4）是价格
            price_str = parts[4]
            return float(price_str)
    except (ValueError, IndexError, AttributeError) as e:
        logger.error(f"提取行李价格失败: {select_value}, error: {str(e)}")
    return 0.0


def get_pay_result(html: str) -> dict:
    """
    解析支付结果页面
    Args:
        html: 支付结果页面HTML内容
    Returns:
        dict: 包含支付结果信息的字典
    """
    result = {'success': False, 'total_amount': 0, 'paid_amount': 0, 'balance': 0, 'currency_code': '', 'error_msg': ''}

    root_dom = BeautifulSoup(html, 'html.parser')

    # 检查错误信息
    error_msg = get_error_msg(html)
    if error_msg:
        result['error_msg'] = error_msg
        return result

    # 解析账单总结表格
    # 在页面中找到包含计费总结的表格
    tables = root_dom.find_all('table')
    for table in tables:
        # 查找包含"总费用"的行
        rows = table.find_all('tr', {'class': 'grdChrRows'})
        if not rows:
            continue

        for row in rows:
            cells = row.find_all('td')
            if len(cells) == 2:
                label = cells[0].get_text(strip=True)
                value_text = cells[1].get_text(strip=True)

                # 提取金额和币种
                try:
                    amount = float(value_text.split()[0].replace(',', ''))
                    currency = value_text.split()[1]
                    result['currency_code'] = currency

                    if '总费用' in label:
                        result['total_amount'] = amount
                    elif '总付款' in label:
                        result['paid_amount'] = amount
                    elif '余额' in label:
                        result['balance'] = amount
                except (IndexError, ValueError) as e:
                    logger.error(f"解析金额失败: {value_text}, error: {str(e)}")
                    continue

    # 判断支付是否成功
    result['success'] = result['balance'] == 0 and result['paid_amount'] > 0

    logger.debug(f"解析结果: {result}")
    return result


def get_paid_amount_from_edit_res(html: str) -> float:
    """
    从编辑预订页面获取已支付金额
    """
    root_dom = BeautifulSoup(html, 'html.parser')
    rows = root_dom.find_all('tr', {'class': 'grdChrRows'})
    currency_dom = root_dom.find('h3')
    result = {'total_amount': 0, 'paid_amount': 0, 'currency_code': ''}
    if not rows:
        return result
    if currency_dom:
        result['currency_code'] = currency_dom.get_text(strip=True)[:3]
    for row in rows:
        cells = row.find_all('td')
        if len(cells) == 2:
            label = cells[0].get_text(strip=True)
            value_text = cells[1].get_text(strip=True)

            # 提取金额和币种
            try:
                amount = float(value_text.split()[0].replace(',', ''))
                # currency = value_text.split()[1]
                # result['currency_code'] = currency
                if '总费用' in label:
                    result['total_amount'] = amount
                elif '总付款' in label:
                    result['paid_amount'] = amount
            except (IndexError, ValueError) as e:
                logger.error(f"解析金额失败: {value_text}, error: {str(e)}")
                continue
    return result


def get_flight_info_from_edit_res(html: str) -> dict:
    """
    解析编辑预订页面
    """
    flight_info = {}
    html = html.replace('&nbsp;', ' ')
    root_dom = BeautifulSoup(html, 'html.parser')
    flight_dom = root_dom.find('tr', id='grdFlightInfo').find_all('td')
    dep_date = flight_dom[0].get_text(strip=True).split(' ')[0]
    dep_date = datetime.strptime(dep_date, '%d/%m/%Y').strftime('%Y-%m-%d')
    logger.debug(f'dep_date {dep_date}')
    flight_no = flight_dom[1].get_text(strip=True)
    logger.debug(f'flight_no {flight_no}')

    dep_time_text = flight_dom[2].get_text(strip=True)
    # html中有&nbsp; 如何处理
    dep_time = dep_time_text.split(' ')[0] + ':00'
    logger.debug(f'dep_time {dep_time}')
    dep_airport_code = dep_time_text.split(' ')[1]
    logger.debug(f'dep_airport_code {dep_airport_code}')
    arr_time_text = flight_dom[3].get_text(strip=True)
    logger.debug(f'arr_time_text {arr_time_text}')
    arr_time = arr_time_text.split(' ')[0] + ':00'
    logger.debug(f'arr_time {arr_time}')
    arr_airport_code = arr_time_text.split(' ')[-1]
    logger.debug(f'arr_airport_code {arr_airport_code}')
    flight_info['dep_date'] = dep_date
    flight_info['flight_no'] = flight_no
    flight_info['dep_time'] = dep_time
    flight_info['arr_time'] = arr_time
    flight_info['dep_airport_code'] = dep_airport_code
    flight_info['arr_airport_code'] = arr_airport_code
    return flight_info
