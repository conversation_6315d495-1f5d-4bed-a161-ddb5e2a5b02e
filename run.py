import asyncio
from datetime import datetime
import time


from app.config import settings
from loguru import logger
import typer
from app.api import fast_api_app
from app.vz_tasks import celery_app
from app.services import debug_services, helper_service, pre_order_service, vz_hood_service
from commons.consts.currency_type import CurrencyType
from commons.extensions.redis_extras import RedisPool


cli = typer.Typer()


@cli.command()
def debug(loop_times: int = 1):
    """调试"""
    # 重新载入配置，使用'.env.test'
    # settings.__init__(_env_file='.env.test')
    params = {
        "crawler_code": "vz",
        "callback_url": "http://192.168.0.156:9000/api/v1/flight_pre_order/crawler/callback/hood/result",
        "po_rule_code": "PVGBKK_HOOD",
        "dep_airport_code": "PVG",
        "arr_airport_code": "BKK",
        "dep_date": "2025-03-13",
        "flight_no": "VZ3525",
        "min_quantity": 3,
    }
    for _ in range(loop_times):
        try:
            start_time = datetime.now()
            vz_hood = vz_hood_service.VZHoodService()
            # 回调在run_hood内部实现，每次占座成功就回调一次
            result = vz_hood.run_hood(params)
            end_time = datetime.now()

            pre_order_service.hood_callback(result)
        except Exception as e:
            logger.warning(e)
        finally:
            time.sleep(0.001)


@cli.command()
def currency_compare(dep, arr, date, fnb):
    """汇率对比"""
    helper_service.do_currency_compare(dep=dep, arr=arr, date=date, fnb=fnb)


if __name__ == '__main__':
    cli()
