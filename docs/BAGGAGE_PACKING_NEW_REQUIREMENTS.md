# 行李打包新修改需求实现文档

## 📋 需求概述

实现行李打包的新修改需求，主要包括：
1. all_fares的key改为用托运公斤数做key（0kg、20kg、25kg等）
2. 在添加乘客阶段进行价格对比判断
3. 如果20kg打包价格更低，执行后退操作切换到20kg舱位
4. 合理拆分book方法，提高代码可维护性

## 🎯 实现的功能

### 1. all_fares结构调整

**修改前：**
```python
all_fares = {
    "ECO": {...},
    "DLX": {...}
}
```

**修改后：**
```python
all_fares = {
    "0kg": {...},   # ECO舱位（0kg托运行李）
    "20kg": {...},  # DLX舱位（20kg托运行李）
    "25kg": {...}   # 其他舱位（25kg托运行李）
}
```

### 2. book方法重构

将原来的book方法拆分为4个步骤：

1. **_book_step1_add_passengers**: 选择航班和添加乘客
2. **_book_step2_check_dlx_switch**: 判断是否需要后退切换到20kg舱位
3. **_book_step3_switch_to_20kg**: 执行后退操作，切换到20kg舱位
4. **_book_step4_complete_booking**: 完成预订流程

### 3. 价格对比逻辑

在添加乘客后，进行以下判断：
1. 检查是否符合use_dlx_cabin条件
2. 用最低舱位执行到update_baggage_options
3. 从*_select_value中提取行李价格
4. 对比：最低舱位+行李价格 vs 20kg打包价格
5. 如果20kg价格更低，执行后退操作

### 4. 后退操作实现

当需要切换到20kg舱位时：
1. 调用travel_options with button='back'
2. 重新选择20kg舱位的fare_key
3. 重新填写乘客信息
4. 继续预订流程

## 📁 修改的文件

### 1. app/services/agent_helper.py

**新增函数：**
```python
def extract_baggage_price_from_select_value(select_value: str) -> float:
    """从*_select_value中提取行李价格"""
```

**修改函数：**
- `parse_flight`: 调整all_fares的key结构

### 2. app/services/agent_service.py

**重构方法：**
- `book`: 主预订方法，拆分为4个步骤
- `_book_step1_add_passengers`: 添加乘客
- `_book_step2_check_dlx_switch`: 判断是否切换
- `_book_step3_switch_to_20kg`: 执行后退切换
- `_book_step4_complete_booking`: 完成预订

**更新方法：**
- `run_verify_book`: 支持新的all_fares结构
- `run_book`: 支持新的all_fares结构

## 🧪 测试验证

创建了完整的测试文件 `tests/test_baggage_packing_new.py`，包含：

1. **extract_baggage_price_from_select_value函数测试**
   - 正常情况：提取55.3价格
   - 不同价格：提取120.5价格
   - 异常情况：返回0.0

2. **all_fares数据结构测试**
   - 验证0kg和20kg舱位存在
   - 验证舱位类型正确

3. **价格对比逻辑测试**
   - 测试不应该切换的情况
   - 测试应该切换的情况

4. **乘客行李数据结构测试**
   - 验证行李数据格式
   - 验证价格提取功能

## 🔄 工作流程

### 预订流程图

```mermaid
graph TD
    A[开始预订] --> B[选择航班和添加乘客]
    B --> C{符合DLX条件?}
    C -->|否| H[继续原流程]
    C -->|是| D[执行update_baggage_options]
    D --> E[提取行李价格]
    E --> F[价格对比]
    F --> G{20kg价格更低?}
    G -->|否| H[继续原流程]
    G -->|是| I[执行后退操作]
    I --> J[切换到20kg舱位]
    J --> K[重新添加乘客]
    K --> L[使用DLX逻辑完成预订]
    H --> M[完成预订]
    L --> M[完成预订]
```

### 价格对比逻辑

```
最低舱位+行李价格 = ECO_base + ECO_tax + baggage_price
20kg舱位价格 = DLX_base + DLX_tax

if 20kg舱位价格 < 最低舱位+行李价格:
    执行后退操作，切换到20kg舱位
else:
    继续使用最低舱位
```

## 📊 测试结果

```
🚀 开始测试行李打包新修改需求
✅ extract_baggage_price_from_select_value函数测试通过
✅ all_fares数据结构测试通过
✅ 价格对比逻辑测试通过
✅ 乘客行李数据结构测试通过
🎉 所有测试通过！行李打包新修改需求实现正确
```

## 🎯 关键特性

1. **智能价格对比**: 自动对比最低舱位+行李 vs 20kg打包价格
2. **动态舱位切换**: 当20kg价格更优时自动切换
3. **后退操作支持**: 实现了完整的后退重新选择流程
4. **代码模块化**: 将复杂的book方法拆分为清晰的步骤
5. **向后兼容**: 保持原有接口不变，新增可选参数

## 🔧 使用方法

```python
# 调用预订方法，传入all_fares参数
book_rs = sv.book(
    fare_key=fare_key, 
    passengers=passengers, 
    use_dlx_cabin=use_dlx_cabin, 
    all_fares=all_fares  # 新增参数
)
```

## 📈 优势

1. **成本优化**: 自动选择最优价格方案
2. **用户体验**: 透明的价格对比和切换
3. **代码质量**: 模块化设计，易于维护
4. **测试覆盖**: 完整的单元测试保证质量
5. **性能优化**: 只在必要时执行后退操作

## 🚀 部署说明

1. 确保所有依赖包已安装（requirements.txt已包含）
2. 运行测试验证功能正常
3. 部署到生产环境
4. 监控价格对比和切换效果

这个实现完全满足了用户的需求，提供了智能的行李打包价格优化功能！
