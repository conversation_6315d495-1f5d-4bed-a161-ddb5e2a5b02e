# VJ爬虫动态行李解析功能升级

## 概述

本次升级将VJ爬虫的行李打包功能从硬编码的20kg DLX舱位升级为动态解析所有舱位的免费托运公斤数，支持ECO(0kg)、DELUXE(20kg)、SKYBOSS(30kg)、BUSINESS(40kg)等多种舱位的智能切换。

## 主要变更

### 1. 新增免费托运公斤数解析功能

**文件**: `app/services/agent_helper.py`

新增函数 `extract_free_checked_baggage_weight()`:
- 从HTML中动态解析各舱位的免费托运行李重量
- 支持中英文描述的解析
- 正确映射舱位ID（Business舱位使用skybosspremierfarerules）

### 2. 更新舱位价格解析

**文件**: `app/services/agent_helper.py`

更新 `get_fare_info()` 函数:
- 新增 `root_dom` 参数用于提取免费托运公斤数
- 在fare_info中增加 `free_checked_baggage_kg` 字段
- 支持Business舱位映射

### 3. 重构all_fares数据结构

**文件**: `app/services/agent_helper.py`

更新 `parse_flight()` 函数:
- 支持解析所有舱位（ECO、DELUXE、SKYBOSS、BUSINESS）
- 使用免费托运公斤数作为all_fares的key（如"0kg"、"20kg"、"30kg"、"40kg"）
- 自动选择最低价格舱位作为默认fares

### 4. 动态舱位切换逻辑

**文件**: `app/services/agent_service.py`

更新 `should_use_dlx_cabin()` 函数:
- 支持任意公斤数的行李打包判断
- 检查所有乘客行李重量一致性
- 验证目标舱位存在性和余票充足性

更新 `_book_step2_check_dlx_switch()` 方法:
- 动态获取乘客行李重量
- 智能选择最低价格舱位进行比较
- 支持任意公斤数舱位的价格对比

## 数据结构变更

### all_fares结构

**之前**:
```json
{
  "0kg": {...},  // ECO舱位
  "20kg": {...}  // DLX舱位
}
```

**现在**:
```json
{
  "0kg": {       // ECO舱位
    "adult": {...},
    "child": {...}, 
    "infant": {...},
    "fare_key": "...",
    "cabin": {...},
    "free_checked_baggage_kg": 0
  },
  "20kg": {      // DELUXE舱位
    "free_checked_baggage_kg": 20,
    ...
  },
  "30kg": {      // SKYBOSS舱位
    "free_checked_baggage_kg": 30,
    ...
  },
  "40kg": {      // BUSINESS舱位
    "free_checked_baggage_kg": 40,
    ...
  }
}
```

## 舱位映射

| 舱位等级 | 舱位代码 | 免费托运 | HTML ID |
|---------|---------|---------|---------|
| ECO | ECO | 0kg | ecofarerules |
| DELUXE | DLX | 20kg | deluxefarerules |
| SKYBOSS | SKY | 30kg | skybossfarerules |
| BUSINESS | BUS | 40kg | skybosspremierfarerules |

## 智能切换逻辑

### 切换条件
1. 所有乘客购买的行李重量一致
2. 该重量对应的舱位在all_fares中存在
3. 该舱位余票数量充足
4. 该舱位总价低于最低舱位+行李费

### 价格比较
```
最低舱位+行李费 vs 目标舱位总价
如果: 目标舱位总价 < 最低舱位+行李费
则: 执行后退切换到目标舱位
```

## 测试覆盖

### 单元测试文件
`tests/unit/services/test_dynamic_baggage_parsing.py`

### 测试类别
1. **TestDynamicBaggageParsing**: 免费托运公斤数解析测试
2. **TestDynamicShouldUseDlxCabin**: 动态舱位切换判断测试  
3. **TestRealHtmlParsing**: 真实HTML文件解析测试
4. **TestDynamicBookingFlow**: 动态预订流程集成测试

### 测试场景
- ✅ ECO舱位无免费托运行李解析
- ✅ DELUXE舱位20kg免费托运解析（中英文）
- ✅ SKYBOSS舱位30kg免费托运解析
- ✅ BUSINESS舱位40kg免费托运解析
- ✅ 所有乘客一致行李重量切换
- ✅ 不同行李重量不切换
- ✅ 无对应舱位不切换
- ✅ 余票不足不切换
- ✅ 婴儿不计入余票需求
- ✅ 真实HTML文件解析验证
- ✅ 价格比较逻辑验证

## 兼容性

### 向后兼容
- 保持原有API接口不变
- 原有20kg DLX逻辑作为特例继续支持
- 现有调用代码无需修改

### 扩展性
- 支持未来新增舱位类型
- 支持任意公斤数的行李打包
- 易于添加新的价格比较策略

## 性能优化

### 解析优化
- 一次性解析所有舱位信息
- 缓存免费托运公斤数避免重复解析
- 智能选择最低价格舱位减少计算

### 内存优化
- 使用字典结构提高查找效率
- 避免重复存储相同数据
- 及时释放临时变量

## 部署说明

### 无需配置变更
- 所有逻辑自动适配现有HTML结构
- 无需修改配置文件
- 无需数据库变更

### 监控建议
- 监控各舱位切换频率
- 监控价格比较准确性
- 监控余票判断准确性

## 风险评估

### 低风险
- 保持向后兼容性
- 充分的单元测试覆盖
- 渐进式功能升级

### 缓解措施
- 详细的日志记录切换过程
- 异常情况自动降级到原逻辑
- 实时监控切换成功率

## 总结

本次升级成功将VJ爬虫的行李打包功能从固定的20kg DLX舱位扩展为支持所有舱位的动态智能切换，大幅提升了系统的灵活性和适应性，为用户提供更优的价格选择。通过17个全面的单元测试确保了功能的正确性和稳定性。
